from flask import Flask, request, jsonify, send_from_directory, session, abort
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room, disconnect
import pymysql
import os
import datetime
import uuid
import base64
import re
import random
import threading
import time

app = Flask(__name__)
app.secret_key = 'wechat_secret_key'  # 用于session
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 限制上传文件大小为500MB
app.config['UPLOAD_FOLDER'] = 'uploads'  # 上传文件保存目录
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # 允许跨站请求携带cookie
app.config['SESSION_COOKIE_SECURE'] = False  # 开发环境不使用HTTPS
CORS(app, supports_credentials=True)

# 初始化SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 在线用户管理
online_users = {}  # {user_id: {'sid': session_id, 'username': username, 'last_seen': timestamp}}
user_rooms = {}    # {user_id: room_name} 用于管理用户的房间

# 初始化用户在线状态表
def init_user_status_table():
    """初始化用户在线状态相关字段"""
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查并添加last_seen字段
            try:
                cursor.execute('SELECT last_seen FROM user LIMIT 1')
            except Exception:
                cursor.execute('ALTER TABLE user ADD COLUMN last_seen DATETIME DEFAULT NULL')
                conn.commit()

            # 检查并添加is_online字段
            try:
                cursor.execute('SELECT is_online FROM user LIMIT 1')
            except Exception:
                cursor.execute('ALTER TABLE user ADD COLUMN is_online TINYINT(1) DEFAULT 0')
                conn.commit()

    except Exception as e:
        print(f"初始化用户状态表错误: {e}")
    finally:
        conn.close()

# 更新用户在线状态到数据库
def update_user_online_status(user_id, is_online=True):
    """更新用户在线状态到数据库"""
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            now = datetime.datetime.now()
            cursor.execute(
                'UPDATE user SET is_online = %s, last_seen = %s WHERE id = %s',
                (1 if is_online else 0, now, user_id)
            )
            conn.commit()
    except Exception as e:
        print(f"更新用户在线状态错误: {e}")
    finally:
        conn.close()

# 获取用户在线状态
def get_user_online_status(user_ids):
    """获取多个用户的在线状态"""
    if not user_ids:
        return {}

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            placeholders = ','.join(['%s'] * len(user_ids))
            cursor.execute(
                f'SELECT id, is_online, last_seen FROM user WHERE id IN ({placeholders})',
                user_ids
            )
            results = cursor.fetchall()

            status_dict = {}
            for row in results:
                # 只检查内存中的在线状态（更准确，只有真正连接WebSocket的用户才算在线）
                is_online_memory = row['id'] in online_users
                status_dict[row['id']] = {
                    'is_online': is_online_memory,  # 只依赖内存中的在线状态
                    'last_seen': row['last_seen'].isoformat() if row['last_seen'] else None
                }

            return status_dict
    except Exception as e:
        print(f"获取用户在线状态错误: {e}")
        return {}
    finally:
        conn.close()

# 确保上传目录存在
def ensure_upload_directories():
    upload_dirs = [
        app.config['UPLOAD_FOLDER'],
        os.path.join(app.config['UPLOAD_FOLDER'], 'images'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'videos'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'files'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'thumbnails'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'albums'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'temp')
    ]
    for directory in upload_dirs:
        os.makedirs(directory, exist_ok=True)

# 初始化上传目录
ensure_upload_directories()

# MySQL配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'wechat',
    'port': 3306,
    'charset': 'utf8mb4'
}

def get_db_conn():
    return pymysql.connect(**MYSQL_CONFIG, cursorclass=pymysql.cursors.DictCursor)

# 初始化用户状态表
init_user_status_table()

# 初始化好友关系表
def init_friends_table():
    """初始化好友关系表"""
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查好友关系表是否存在
            cursor.execute("SHOW TABLES LIKE 'friends'")
            if not cursor.fetchone():
                cursor.execute('''
                    CREATE TABLE friends (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        friend_id INT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE KEY unique_friendship (user_id, friend_id),
                        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
                        FOREIGN KEY (friend_id) REFERENCES user(id) ON DELETE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                ''')
                conn.commit()
    except Exception as e:
        print(f"初始化好友关系表错误: {e}")
    finally:
        conn.close()

# 初始化好友关系表
init_friends_table()

# 初始化群聊消息已读状态表
def init_group_message_reads_table():
    """初始化群聊消息已读状态表"""
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查group_message_reads表是否存在
            cursor.execute("SHOW TABLES LIKE 'group_message_reads'")
            if not cursor.fetchone():
                cursor.execute("""
                    CREATE TABLE group_message_reads (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        message_id INT NOT NULL,
                        user_id INT NOT NULL,
                        read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (message_id) REFERENCES group_messages(id) ON DELETE CASCADE,
                        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_message_read (message_id, user_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)
                conn.commit()
    except Exception as e:
        print(f"初始化群聊消息已读状态表错误: {e}")
        # 如果是因为group_messages表不存在导致的外键约束错误，先创建一个简化版本
        try:
            cursor.execute("SHOW TABLES LIKE 'group_messages'")
            if not cursor.fetchone():
                pass  # group_messages表不存在，跳过group_message_reads表创建
            else:
                # group_messages表存在，但创建失败，可能是其他原因
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS group_message_reads (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        message_id INT NOT NULL,
                        user_id INT NOT NULL,
                        read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE KEY unique_message_read (message_id, user_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)
                conn.commit()
        except Exception as e2:
            print(f"创建简化版群聊消息已读状态表也失败: {e2}")
    finally:
        conn.close()

# 初始化群聊消息已读状态表
init_group_message_reads_table()

# 初始化聊天设置表
def init_chat_settings_table():
    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 检查chat_settings表是否存在
        cursor.execute("SHOW TABLES LIKE 'chat_settings'")
        if not cursor.fetchone():

            cursor.execute('''
                CREATE TABLE chat_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    chat_id INT NOT NULL,
                    muted BOOLEAN DEFAULT FALSE,
                    pinned BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_user_chat (user_id, chat_id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_chat_id (chat_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ''')
            conn.commit()
    except Exception as e:
        print(f"创建聊天设置表失败: {e}")
    finally:
        conn.close()

# 初始化聊天设置表
init_chat_settings_table()

# 数据库迁移：添加缺失的字段
def migrate_database():
    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 检查并添加group_members表的role字段
        cursor.execute("DESCRIBE group_members")
        columns = cursor.fetchall()
        has_role = any(col['Field'] == 'role' for col in columns)

        if not has_role:
            cursor.execute("""
                ALTER TABLE group_members
                ADD COLUMN role ENUM('owner', 'admin', 'member') DEFAULT 'member'
            """)
            conn.commit()

        # 检查并添加user表的online_status字段
        cursor.execute("DESCRIBE user")
        columns = cursor.fetchall()
        has_online_status = any(col['Field'] == 'online_status' for col in columns)

        if not has_online_status:
            cursor.execute("""
                ALTER TABLE user
                ADD COLUMN online_status BOOLEAN DEFAULT FALSE
            """)
            conn.commit()

    except Exception as e:
        print(f"数据库迁移失败: {e}")
    finally:
        conn.close()

# 执行数据库迁移
migrate_database()

# 初始化测试数据
def init_test_data():
    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 检查是否已有用户数据
        cursor.execute("SELECT COUNT(*) as count FROM user")
        user_count = cursor.fetchone()['count']

        if user_count < 3:  # 如果用户少于3个，创建测试用户
            # 创建测试用户
            test_users = [
                ('wxy', 'wxy123'),
                ('king', 'king123'),
                ('test', 'test123')
            ]

            for username, password in test_users:
                cursor.execute("""
                    INSERT IGNORE INTO user (username, password, online_status)
                    VALUES (%s, %s, TRUE)
                """, (username, password))

            conn.commit()

            # 创建好友关系
            cursor.execute("SELECT id, username FROM user ORDER BY id LIMIT 3")
            users = cursor.fetchall()

            if len(users) >= 3:
                # 让第一个用户和其他用户成为好友
                user1_id = users[0]['id']
                for i in range(1, len(users)):
                    user2_id = users[i]['id']

                    # 双向好友关系
                    cursor.execute("""
                        INSERT IGNORE INTO friends (user_id, friend_id, created_at)
                        VALUES (%s, %s, NOW())
                    """, (user1_id, user2_id))

                    cursor.execute("""
                        INSERT IGNORE INTO friends (user_id, friend_id, created_at)
                        VALUES (%s, %s, NOW())
                    """, (user2_id, user1_id))

                conn.commit()

                # 创建测试群聊
                cursor.execute("""
                    INSERT IGNORE INTO `groups` (name, owner_id, created_at)
                    VALUES ('同学群', %s, NOW())
                """, (user1_id,))

                # 获取群聊ID
                cursor.execute("SELECT LAST_INSERT_ID() as group_id")
                result = cursor.fetchone()
                if result and result['group_id'] > 0:
                    group_id = result['group_id']

                    # 添加群主
                    cursor.execute("""
                        INSERT IGNORE INTO group_members (group_id, user_id, role, joined_at)
                        VALUES (%s, %s, 'owner', NOW())
                    """, (group_id, user1_id))

                    # 添加其他成员
                    for i in range(1, len(users)):
                        user2_id = users[i]['id']
                        cursor.execute("""
                            INSERT IGNORE INTO group_members (group_id, user_id, role, joined_at)
                            VALUES (%s, %s, 'member', NOW())
                        """, (group_id, user2_id))

                    conn.commit()

    except Exception as e:
        print(f"初始化测试数据失败: {e}")
    finally:
        conn.close()

# 初始化测试数据
init_test_data()

# 生成唯一的6位数字微信号
def generate_unique_wechat_id():
    """生成唯一的6位数字微信号"""
    conn = get_db_conn()
    max_attempts = 100  # 最大尝试次数，避免无限循环

    for _ in range(max_attempts):
        # 生成6位随机数字
        wechat_id = str(random.randint(100000, 999999))

        try:
            with conn.cursor() as cursor:
                # 检查微信号是否已存在
                cursor.execute('SELECT id FROM user WHERE wechat_id = %s', (wechat_id,))
                if not cursor.fetchone():
                    conn.close()
                    return wechat_id
        except Exception as e:
            # 如果字段不存在，说明是第一次使用，直接返回
            if 'wechat_id' in str(e):
                conn.close()
                return wechat_id
            print(f"检查微信号唯一性时出错: {e}")

    conn.close()
    # 如果尝试多次都失败，抛出异常
    raise Exception("无法生成唯一的微信号，请稍后重试")

# 静态文件服务将在所有API路由之后定义

# 注册接口
@app.route('/api/register', methods=['POST'])
def register():
    data = request.json
    username = data.get('username')
    password = data.get('password')
    avatar = data.get('avatar', '')

    if not username or not password:
        return jsonify({'code': 1, 'msg': '用户名和密码不能为空'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            cursor.execute('SELECT id FROM user WHERE username=%s', (username,))
            if cursor.fetchone():
                conn.close()
                return jsonify({'code': 2, 'msg': '用户名已存在'}), 400

            # 生成唯一的微信号
            try:
                wechat_id = generate_unique_wechat_id()
            except Exception as e:
                conn.close()
                return jsonify({'code': 3, 'msg': f'生成微信号失败: {str(e)}'}), 500

            now = datetime.datetime.now()

            # 尝试插入包含微信号的用户记录
            try:
                cursor.execute('INSERT INTO user (username, password, avatar, wechat_id, created_at) VALUES (%s, %s, %s, %s, %s)',
                               (username, password, avatar, wechat_id, now))
                conn.commit()
            except Exception as e:
                # 如果wechat_id字段不存在，先添加字段再插入
                if 'wechat_id' in str(e):
                    try:
                        cursor.execute('ALTER TABLE user ADD COLUMN wechat_id VARCHAR(6) UNIQUE')
                        conn.commit()

                        # 重新尝试插入
                        cursor.execute('INSERT INTO user (username, password, avatar, wechat_id, created_at) VALUES (%s, %s, %s, %s, %s)',
                                       (username, password, avatar, wechat_id, now))
                        conn.commit()
                    except Exception as alter_e:
                        print(f"添加wechat_id字段失败: {alter_e}")
                        conn.rollback()
                        conn.close()
                        return jsonify({'code': 4, 'msg': '数据库结构错误，请联系管理员'}), 500
                else:
                    conn.rollback()
                    conn.close()
                    return jsonify({'code': 5, 'msg': f'注册失败: {str(e)}'}), 500

        conn.close()
        return jsonify({'code': 0, 'msg': '注册成功'})
    except Exception as e:
        conn.rollback()
        conn.close()
        return jsonify({'code': 6, 'msg': f'注册失败: {str(e)}'}), 500

# 登录接口
@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    username = data.get('username')
    password = data.get('password')

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            cursor.execute('SELECT * FROM user WHERE username=%s', (username,))
            user = cursor.fetchone()
        conn.close()
        if not user or user['password'] != password:
            return jsonify({'code': 1, 'msg': '用户名或密码错误'}), 400

        session['user_id'] = user['id']

        # 确保微信号存在
        wechat_id = user.get('wechat_id')
        if not wechat_id:
            try:
                wechat_id = generate_unique_wechat_id()
                conn = get_db_conn()
                with conn.cursor() as cursor:
                    cursor.execute('UPDATE user SET wechat_id = %s WHERE id = %s', (wechat_id, user['id']))
                    conn.commit()
                conn.close()
            except Exception as e:
                print(f"为用户生成微信号失败: {e}")
                wechat_id = None

        return jsonify({'code': 0, 'msg': '登录成功', 'user': {
            'id': user['id'],
            'username': user['username'],
            'avatar': user['avatar'],
            'wechatId': wechat_id  # 使用驼峰命名以匹配前端
        }})
    except Exception as e:
        return jsonify({'code': 2, 'msg': f'登录失败: {str(e)}'}), 500

# 获取当前用户信息
@app.route('/api/userinfo')
def userinfo():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 尝试查询包含微信号的用户信息
            try:
                cursor.execute('SELECT id, username, avatar, wechat_id, created_at FROM user WHERE id=%s', (user_id,))
                user = cursor.fetchone()
            except Exception as e:
                # 如果wechat_id字段不存在，先添加字段
                if 'wechat_id' in str(e):
                    print("wechat_id字段不存在，正在添加...")
                    try:
                        cursor.execute('ALTER TABLE user ADD COLUMN wechat_id VARCHAR(6) UNIQUE')
                        conn.commit()
                        print("成功添加wechat_id字段")

                        # 为现有用户生成微信号
                        cursor.execute('SELECT id FROM user WHERE wechat_id IS NULL OR wechat_id = ""')
                        users_without_wechat_id = cursor.fetchall()

                        for existing_user in users_without_wechat_id:
                            try:
                                new_wechat_id = generate_unique_wechat_id()
                                cursor.execute('UPDATE user SET wechat_id = %s WHERE id = %s',
                                             (new_wechat_id, existing_user['id']))
                            except Exception as gen_e:
                                print(f"为用户 {existing_user['id']} 生成微信号失败: {gen_e}")

                        conn.commit()

                        # 重新查询用户信息
                        cursor.execute('SELECT id, username, avatar, wechat_id, created_at FROM user WHERE id=%s', (user_id,))
                        user = cursor.fetchone()
                    except Exception as alter_e:
                        print(f"添加wechat_id字段失败: {alter_e}")
                        # 如果添加字段失败，使用原来的查询
                        cursor.execute('SELECT id, username, avatar, created_at FROM user WHERE id=%s', (user_id,))
                        user = cursor.fetchone()
                        if user:
                            user['wechat_id'] = None
                else:
                    raise e

        conn.close()
        if not user:
            return jsonify({'code': 2, 'msg': '用户不存在'}), 404

        # 确保微信号存在，如果不存在则生成一个
        if not user.get('wechat_id'):
            try:
                wechat_id = generate_unique_wechat_id()
                conn = get_db_conn()
                with conn.cursor() as cursor:
                    cursor.execute('UPDATE user SET wechat_id = %s WHERE id = %s', (wechat_id, user_id))
                    conn.commit()
                conn.close()
                user['wechat_id'] = wechat_id
            except Exception as e:
                print(f"为用户生成微信号失败: {e}")
                user['wechat_id'] = None

        # 添加驼峰命名的字段以兼容前端
        user['wechatId'] = user.get('wechat_id')

        return jsonify({'code': 0, 'user': user})
    except Exception as e:
        conn.close()
        return jsonify({'code': 3, 'msg': f'获取用户信息失败: {str(e)}'}), 500

# 获取个人信息（兼容前端）
@app.route('/api/profile')
def get_profile():
    """获取个人信息，兼容前端下拉刷新功能"""
    return userinfo()

# 更新用户信息
@app.route('/api/profile/update', methods=['POST'])
def update_profile():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    username = data.get('username', '').strip()
    avatar_data = data.get('avatar')

    if not username:
        return jsonify({'code': 2, 'msg': '用户名不能为空'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查用户是否存在
            cursor.execute('SELECT id, avatar FROM user WHERE id = %s', (user_id,))
            user = cursor.fetchone()
            if not user:
                return jsonify({'code': 3, 'msg': '用户不存在'}), 404

            # 处理头像上传
            avatar_url = user['avatar']  # 保持原头像
            if avatar_data and avatar_data.startswith('data:image'):
                # 保存新头像
                new_avatar_url = save_base64_image(avatar_data, f"avatar_{user_id}")
                if new_avatar_url:
                    avatar_url = new_avatar_url
                else:
                    return jsonify({'code': 4, 'msg': '头像上传失败'}), 500

            # 更新用户信息
            cursor.execute(
                'UPDATE user SET username = %s, avatar = %s WHERE id = %s',
                (username, avatar_url, user_id)
            )
            conn.commit()

            # 返回更新后的用户信息，包含微信号
            try:
                cursor.execute('SELECT id, username, avatar, wechat_id FROM user WHERE id = %s', (user_id,))
                updated_user = cursor.fetchone()
            except Exception as e:
                # 如果wechat_id字段不存在，使用原查询
                if 'wechat_id' in str(e):
                    cursor.execute('SELECT id, username, avatar FROM user WHERE id = %s', (user_id,))
                    updated_user = cursor.fetchone()
                    if updated_user:
                        updated_user['wechat_id'] = None
                else:
                    raise e

            # 添加驼峰命名的字段以兼容前端
            updated_user['wechatId'] = updated_user.get('wechat_id')

            return jsonify({
                'code': 0,
                'msg': '更新成功',
                'user': updated_user
            })
    except Exception as e:
        conn.rollback()
        import traceback
        traceback.print_exc()
        return jsonify({'code': 5, 'msg': f'更新失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取聊天列表
@app.route('/api/chats')
def get_chats():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401
    
    conn = get_db_conn()
    with conn.cursor() as cursor:
        # 查询与当前用户相关的最近一条消息，排除已删除的聊天
        query = """
        SELECT
            m.sender_id, m.receiver_id, m.content, m.created_at, m.read_status,
            CASE
                WHEN m.sender_id = %s THEN m.receiver_id
                ELSE m.sender_id
            END AS chat_with_id,
            u.username as chat_with_name,
            u.avatar as chat_with_avatar,
            (SELECT COUNT(*) FROM messages m2
             WHERE m2.sender_id = CASE
                WHEN m.sender_id = %s THEN m.receiver_id
                ELSE m.sender_id
             END AND m2.receiver_id = %s AND m2.read_status = 0) as unread_count,
            'individual' as chat_type,
            COALESCE(cs.muted, 0) as muted,
            COALESCE(cs.pinned, 0) as pinned
        FROM messages m
        JOIN user u ON u.id = CASE
            WHEN m.sender_id = %s THEN m.receiver_id
            ELSE m.sender_id
        END
        LEFT JOIN chat_settings cs ON cs.user_id = %s AND cs.chat_id = CASE
            WHEN m.sender_id = %s THEN m.receiver_id
            ELSE m.sender_id
        END
        JOIN (
            SELECT
                CASE
                    WHEN sender_id = %s THEN receiver_id
                    ELSE sender_id
                END AS user_id,
                MAX(created_at) as max_date
            FROM messages
            WHERE sender_id = %s OR receiver_id = %s
            GROUP BY user_id
        ) as latest ON (
            (m.sender_id = %s AND m.receiver_id = latest.user_id) OR
            (m.sender_id = latest.user_id AND m.receiver_id = %s)
        ) AND m.created_at = latest.max_date
        LEFT JOIN chat_deletions cd ON (cd.user_id = %s AND cd.chat_with_id = CASE
            WHEN m.sender_id = %s THEN m.receiver_id
            ELSE m.sender_id
        END)
        WHERE cd.id IS NULL OR cd.deleted_at < m.created_at
        ORDER BY cs.pinned DESC, m.created_at DESC
        """
        cursor.execute(query, (user_id, user_id, user_id, user_id, user_id, user_id, user_id, user_id, user_id, user_id, user_id, user_id, user_id))
        individual_chats = cursor.fetchall()
        
        # 查询用户参与的群聊
        group_chats = []
        try:
            cursor.execute("SELECT 1 FROM `groups` LIMIT 1")
            # 群聊表存在，查询群聊
            cursor.execute("""
                SELECT g.id, g.name, g.avatar, g.created_at,
                       COUNT(DISTINCT gm.user_id) as member_count,
                       COALESCE(latest_msg.content, '群聊已创建') as last_message,
                       COALESCE(latest_msg.created_at, g.created_at) as last_message_time,
                       COALESCE(unread_count.unread, 0) as unread_count,
                       COALESCE(cs.muted, 0) as muted,
                       COALESCE(cs.pinned, 0) as pinned
                FROM `groups` g
                JOIN group_members gm1 ON g.id = gm1.group_id AND gm1.user_id = %s
                LEFT JOIN group_members gm ON g.id = gm.group_id
                LEFT JOIN chat_settings cs ON cs.user_id = %s AND cs.chat_id = g.id
                LEFT JOIN (
                    SELECT group_id, content, created_at,
                           ROW_NUMBER() OVER (PARTITION BY group_id ORDER BY created_at DESC) as rn
                    FROM group_messages
                ) latest_msg ON g.id = latest_msg.group_id AND latest_msg.rn = 1
                LEFT JOIN (
                    SELECT gm_inner.group_id,
                           COUNT(gm_inner.id) as unread
                    FROM group_messages gm_inner
                    WHERE gm_inner.sender_id != %s
                    AND gm_inner.id NOT IN (
                        SELECT gmr.message_id
                        FROM group_message_reads gmr
                        WHERE gmr.user_id = %s
                    )
                    GROUP BY gm_inner.group_id
                ) unread_count ON g.id = unread_count.group_id
                GROUP BY g.id, g.name, g.avatar, g.created_at, latest_msg.content, latest_msg.created_at, unread_count.unread, cs.muted, cs.pinned
                ORDER BY cs.pinned DESC, COALESCE(latest_msg.created_at, g.created_at) DESC
            """, (user_id, user_id, user_id, user_id))
            group_chats = cursor.fetchall()
        except:
            # 群聊表不存在，跳过群聊查询
            pass
        
        # 格式化结果
        formatted_chats = []
        
        # 处理个人聊天
        for chat in individual_chats:
            # 格式化时间
            created_at = chat['created_at']
            now = datetime.datetime.now()
            if created_at.date() == now.date():
                time_str = created_at.strftime('%H:%M')
            elif (now.date() - created_at.date()).days == 1:
                time_str = '昨天'
            else:
                time_str = created_at.strftime('%m-%d')
            
            formatted_chats.append({
                'id': chat['chat_with_id'],
                'name': chat['chat_with_name'],
                'avatar': chat['chat_with_avatar'] or 'https://picsum.photos/seed/user/100/100',
                'lastMessage': chat['content'],
                'time': time_str,
                'unread': chat['unread_count'],
                'type': 'individual',
                'muted': bool(chat['muted']),  # 免打扰状态
                'pinned': bool(chat['pinned'])  # 置顶状态
            })
        
        # 处理群聊
        for group in group_chats:
            # 格式化时间
            created_at = group['last_message_time']
            now = datetime.datetime.now()
            if created_at.date() == now.date():
                time_str = created_at.strftime('%H:%M')
            elif (now.date() - created_at.date()).days == 1:
                time_str = '昨天'
            else:
                time_str = created_at.strftime('%m-%d')

            # 获取群成员头像，确保群主排在第一位
            try:
                with conn.cursor() as member_cursor:
                    member_cursor.execute("""
                        SELECT u.avatar, u.username, gm.role
                        FROM group_members gm
                        JOIN user u ON gm.user_id = u.id
                        WHERE gm.group_id = %s
                        ORDER BY
                            CASE WHEN gm.role = 'owner' THEN 0 ELSE 1 END,
                            gm.id ASC
                        LIMIT 9
                    """, (group['id'],))
                    members = member_cursor.fetchall()

                # 处理成员头像，为没有头像的成员生成字母占位符
                member_avatars = []
                for member in members:
                    if member['avatar'] and member['avatar'].strip():
                        member_avatars.append(member['avatar'])
                    else:
                        # 生成字母占位符
                        name = member['username'] or ''
                        initial = name[0].upper() if name else '?'
                        member_avatars.append(f'letter:{initial}')
            except Exception as e:
                print(f"获取群成员头像时出错: {e}")
                member_avatars = []

            formatted_chats.append({
                'id': group['id'],
                'name': f"{group['name']}({group['member_count']})",
                'avatar': group['avatar'],  # 返回真实头像或空值，让前端处理默认显示
                'lastMessage': group['last_message'],
                'time': time_str,
                'unread': group['unread_count'],  # 使用计算出的未读消息数
                'type': 'group',
                'members': group['member_count'],
                'member_avatars': member_avatars,  # 添加成员头像数组，群主排在第一位
                'muted': bool(group['muted']),  # 免打扰状态
                'pinned': bool(group['pinned'])  # 置顶状态
            })
        
        # 按置顶状态和最新消息时间排序（置顶的聊天排在前面）
        formatted_chats.sort(key=lambda x: (not x.get('pinned', False), x['time']), reverse=False)
        
    conn.close()
    
    return jsonify({'code': 0, 'data': formatted_chats})

# 获取与特定用户的聊天消息
@app.route('/api/messages/<int:chat_with_id>')
def get_messages(chat_with_id):
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'code': 1, 'msg': '未登录'}), 401
        
        conn = get_db_conn()
        with conn.cursor() as cursor:
            # 获取未读消息ID列表，用于WebSocket通知
            cursor.execute(
                'SELECT id FROM messages WHERE sender_id = %s AND receiver_id = %s AND read_status = 0',
                (chat_with_id, user_id)
            )
            unread_message_ids = [row['id'] for row in cursor.fetchall()]

            # 标记消息为已读
            cursor.execute(
                'UPDATE messages SET read_status = 1 WHERE sender_id = %s AND receiver_id = %s AND read_status = 0',
                (chat_with_id, user_id)
            )
            conn.commit()

            # 如果有未读消息被标记为已读，通过WebSocket通知发送方
            if unread_message_ids and chat_with_id in user_rooms:
                socketio.emit('messages_read', {
                    'message_ids': unread_message_ids,
                    'reader_id': user_id,
                    'timestamp': datetime.datetime.now().isoformat()
                }, room=user_rooms[chat_with_id])
            
            # 获取聊天对象信息
            cursor.execute('SELECT id, username, avatar FROM user WHERE id = %s', (chat_with_id,))
            chat_with = cursor.fetchone()
            if not chat_with:
                conn.close()
                return jsonify({'code': 2, 'msg': '用户不存在'}), 404
            
            # 获取最近的50条消息
            cursor.execute(
                """
                SELECT id, sender_id, receiver_id, content, message_type, file_url, file_name, file_size, created_at, read_status
                FROM messages
                WHERE (sender_id = %s AND receiver_id = %s) OR (sender_id = %s AND receiver_id = %s)
                ORDER BY created_at DESC
                LIMIT 50
                """,
                (user_id, chat_with_id, chat_with_id, user_id)
            )
            messages = list(cursor.fetchall())
            messages.reverse()  # 按时间正序排列
            
            # 格式化消息
            formatted_messages = []
            for msg in messages:
                # 判断消息类型
                msg_type = 'sent' if msg['sender_id'] == user_id else 'received'
                
                # 格式化时间
                created_at = msg['created_at']
                now = datetime.datetime.now()
                if created_at.date() == now.date():
                    time_str = created_at.strftime('%H:%M')
                elif (now.date() - created_at.date()).days == 1:
                    time_str = '昨天 ' + created_at.strftime('%H:%M')
                else:
                    time_str = created_at.strftime('%m-%d %H:%M')
                
                formatted_messages.append({
                    'id': msg['id'],
                    'type': msg_type,
                    'content': msg['content'],
                    'message_type': msg['message_type'] or 'text',
                    'file_url': msg['file_url'],
                    'file_name': msg['file_name'],
                    'file_size': msg['file_size'],
                    'time': time_str,
                    'read': msg['read_status'] == 1
                })
        conn.close()
        
        return jsonify({
            'code': 0,
            'data': {
                'chat': {
                    'id': chat_with['id'],
                    'name': chat_with['username'],
                    'avatar': chat_with['avatar'] or 'https://picsum.photos/seed/user/100/100'
                },
                'messages': formatted_messages
            }
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'code': 500, 'msg': str(e)}), 500

# 文件上传API
@app.route('/api/upload/file', methods=['POST'])
def upload_file():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    if 'file' not in request.files:
        return jsonify({'code': 2, 'msg': '没有文件'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'code': 3, 'msg': '没有选择文件'}), 400

    try:
        # 生成唯一文件名
        file_ext = os.path.splitext(file.filename)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}{file_ext}"

        # 根据文件类型确定存储目录
        if file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'images')
            file_type = 'image'
        elif file_ext in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'videos')
            file_type = 'video'
        else:
            upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'files')
            file_type = 'file'

        # 确保上传目录存在
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # 获取文件大小
        file_size = os.path.getsize(file_path)

        # 返回文件信息
        file_url = os.path.join(upload_dir.replace('\\', '/').replace(app.config['UPLOAD_FOLDER'], 'uploads'), unique_filename).replace('\\', '/')

        return jsonify({
            'code': 0,
            'msg': '上传成功',
            'data': {
                'file_url': file_url,
                'file_name': file.filename,
                'file_size': file_size,
                'file_type': file_type
            }
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'code': 4, 'msg': f'上传失败: {str(e)}'}), 500

# 发送消息
@app.route('/api/messages/send', methods=['POST'])
def send_message():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    receiver_id = data.get('receiver_id')
    content = data.get('content', '')
    message_type = data.get('message_type', 'text')
    file_url = data.get('file_url')
    file_name = data.get('file_name')
    file_size = data.get('file_size')

    if not receiver_id:
        return jsonify({'code': 2, 'msg': '接收者ID不能为空'}), 400

    # 验证消息内容
    if message_type == 'text' and not content:
        return jsonify({'code': 3, 'msg': '文本消息内容不能为空'}), 400
    elif message_type in ['image', 'video', 'file'] and not file_url:
        return jsonify({'code': 4, 'msg': '文件消息必须包含文件URL'}), 400
    
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查接收者是否存在
            cursor.execute('SELECT id FROM user WHERE id = %s', (receiver_id,))
            if not cursor.fetchone():
                return jsonify({'code': 3, 'msg': '接收者不存在'}), 404
            
            # 插入消息
            now = datetime.datetime.now()
            cursor.execute(
                '''INSERT INTO messages (sender_id, receiver_id, content, message_type, file_url, file_name, file_size, created_at)
                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s)''',
                (user_id, receiver_id, content, message_type, file_url, file_name, file_size, now)
            )
            message_id = cursor.lastrowid

            # 删除接收方的聊天删除记录（如果存在），让聊天重新出现在接收方的聊天列表中
            cursor.execute(
                'DELETE FROM chat_deletions WHERE user_id = %s AND chat_with_id = %s',
                (receiver_id, user_id)
            )

            conn.commit()

            # 返回已格式化的消息
            time_str = now.strftime('%H:%M')

            # 获取发送者信息用于WebSocket推送
            cursor.execute('SELECT username, avatar FROM user WHERE id = %s', (user_id,))
            sender_info = cursor.fetchone()

            # 准备WebSocket消息数据
            websocket_message = {
                'id': message_id,
                'type': 'received',  # 对接收方来说是接收的消息
                'content': content,
                'message_type': message_type,
                'file_url': file_url,
                'file_name': file_name,
                'file_size': file_size,
                'time': time_str,
                'read': False,
                'sender_id': user_id,
                'sender_name': sender_info['username'] if sender_info else '',
                'sender_avatar': sender_info['avatar'] if sender_info else ''
            }

            # 通过WebSocket实时推送消息给接收方
            send_message_via_websocket(user_id, receiver_id, websocket_message)

        return jsonify({
            'code': 0,
            'msg': '发送成功',
            'data': {
                'id': message_id,
                'type': 'sent',
                'content': content,
                'message_type': message_type,
                'file_url': file_url,
                'file_name': file_name,
                'file_size': file_size,
                'time': time_str,
                'read': False
            }
        })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 4, 'msg': f'发送失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取用户列表用于发起新聊天
@app.route('/api/users')
def get_users():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    with conn.cursor() as cursor:
        cursor.execute('SELECT id, username, avatar FROM user WHERE id != %s', (user_id,))
        users = cursor.fetchall()

        for user in users:
            if not user['avatar']:
                user['avatar'] = f"https://picsum.photos/seed/user{user['id']}/100/100"
    conn.close()

    return jsonify({'code': 0, 'data': users})

# 搜索用户接口
@app.route('/api/users/search')
def search_users():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    query = request.args.get('q', '').strip()
    if not query:
        return jsonify({'code': 2, 'msg': '搜索关键词不能为空'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 搜索用户名或微信号匹配的用户
            try:
                cursor.execute('''
                    SELECT id, username, avatar, wechat_id
                    FROM user
                    WHERE id != %s AND (username LIKE %s OR wechat_id LIKE %s)
                    ORDER BY username
                    LIMIT 20
                ''', (user_id, f'%{query}%', f'%{query}%'))
                users = cursor.fetchall()
            except Exception as e:
                # 如果wechat_id字段不存在，只搜索用户名
                if 'wechat_id' in str(e):
                    cursor.execute('''
                        SELECT id, username, avatar
                        FROM user
                        WHERE id != %s AND username LIKE %s
                        ORDER BY username
                        LIMIT 20
                    ''', (user_id, f'%{query}%'))
                    users = cursor.fetchall()
                    for user in users:
                        user['wechat_id'] = None
                else:
                    raise e

            # 检查哪些用户已经是好友
            if users:
                user_ids = [user['id'] for user in users]
                placeholders = ','.join(['%s'] * len(user_ids))
                cursor.execute(f'''
                    SELECT friend_id FROM friends
                    WHERE user_id = %s AND friend_id IN ({placeholders})
                ''', [user_id] + user_ids)
                friend_ids = {row['friend_id'] for row in cursor.fetchall()}

                # 为每个用户添加是否已是好友的标识
                for user in users:
                    user['is_friend'] = user['id'] in friend_ids
                    if not user['avatar']:
                        user['avatar'] = f"https://picsum.photos/seed/user{user['id']}/100/100"

            return jsonify({'code': 0, 'data': users})
    except Exception as e:
        return jsonify({'code': 3, 'msg': f'搜索失败: {str(e)}'}), 500
    finally:
        conn.close()

# 添加好友接口
@app.route('/api/friends/add', methods=['POST'])
def add_friend():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    friend_id = data.get('friend_id')

    if not friend_id:
        return jsonify({'code': 2, 'msg': '好友ID不能为空'}), 400

    if friend_id == user_id:
        return jsonify({'code': 3, 'msg': '不能添加自己为好友'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查要添加的用户是否存在
            cursor.execute('SELECT id, username FROM user WHERE id = %s', (friend_id,))
            friend = cursor.fetchone()
            if not friend:
                return jsonify({'code': 4, 'msg': '用户不存在'}), 404

            # 检查是否已经是好友
            cursor.execute('SELECT id FROM friends WHERE user_id = %s AND friend_id = %s', (user_id, friend_id))
            if cursor.fetchone():
                return jsonify({'code': 5, 'msg': '已经是好友了'}), 400

            # 添加双向好友关系
            now = datetime.datetime.now()
            cursor.execute('INSERT INTO friends (user_id, friend_id, created_at) VALUES (%s, %s, %s)', (user_id, friend_id, now))
            cursor.execute('INSERT INTO friends (user_id, friend_id, created_at) VALUES (%s, %s, %s)', (friend_id, user_id, now))
            conn.commit()

            return jsonify({
                'code': 0,
                'msg': f'成功添加 {friend["username"]} 为好友',
                'data': {
                    'friend_id': friend_id,
                    'friend_name': friend['username']
                }
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 6, 'msg': f'添加好友失败: {str(e)}'}), 500
    finally:
        conn.close()

# 删除好友接口
@app.route('/api/friends/remove', methods=['POST'])
def remove_friend():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    friend_id = data.get('friend_id')

    if not friend_id:
        return jsonify({'code': 2, 'msg': '好友ID不能为空'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查是否是好友
            cursor.execute('SELECT id FROM friends WHERE user_id = %s AND friend_id = %s', (user_id, friend_id))
            if not cursor.fetchone():
                return jsonify({'code': 3, 'msg': '不是好友关系'}), 400

            # 获取好友信息
            cursor.execute('SELECT username FROM user WHERE id = %s', (friend_id,))
            friend = cursor.fetchone()
            friend_name = friend['username'] if friend else '未知用户'

            # 删除双向好友关系
            cursor.execute('DELETE FROM friends WHERE user_id = %s AND friend_id = %s', (user_id, friend_id))
            cursor.execute('DELETE FROM friends WHERE user_id = %s AND friend_id = %s', (friend_id, user_id))
            conn.commit()

            return jsonify({
                'code': 0,
                'msg': f'已删除好友 {friend_name}',
                'data': {
                    'friend_id': friend_id,
                    'friend_name': friend_name
                }
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 4, 'msg': f'删除好友失败: {str(e)}'}), 500
    finally:
        conn.close()

# 删除聊天会话
@app.route('/api/chats/<int:chat_with_id>', methods=['DELETE'])
def delete_chat(chat_with_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查聊天对象是否存在
            cursor.execute('SELECT id FROM user WHERE id = %s', (chat_with_id,))
            if not cursor.fetchone():
                return jsonify({'code': 2, 'msg': '用户不存在'}), 404

            # 检查是否已经删除过
            cursor.execute(
                'SELECT id FROM chat_deletions WHERE user_id = %s AND chat_with_id = %s',
                (user_id, chat_with_id)
            )
            if cursor.fetchone():
                return jsonify({'code': 3, 'msg': '聊天已删除'}), 400

            # 插入删除记录
            now = datetime.datetime.now()
            cursor.execute(
                'INSERT INTO chat_deletions (user_id, chat_with_id, deleted_at) VALUES (%s, %s, %s)',
                (user_id, chat_with_id, now)
            )
            conn.commit()

            return jsonify({
                'code': 0,
                'msg': '聊天删除成功'
            })
    except Exception as e:
        conn.rollback()
        import traceback
        traceback.print_exc()
        return jsonify({'code': 4, 'msg': f'删除失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取好友列表接口
@app.route('/api/friends', methods=['GET'])
def get_friends():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 获取好友列表
        cursor.execute("""
            SELECT u.id, u.username, u.avatar, u.online_status
            FROM friends f
            JOIN user u ON (f.friend_id = u.id)
            WHERE f.user_id = %s
            ORDER BY u.username
        """, (user_id,))

        friends = []
        for row in cursor.fetchall():
            friends.append({
                'id': row['id'],
                'name': row['username'],
                'username': row['username'],
                'avatar': row['avatar'],
                'online': row['online_status'] == 1 if row['online_status'] is not None else False
            })

        return jsonify({'code': 0, 'data': friends})

    except Exception as e:
        print(f"获取好友列表失败: {str(e)}")
        return jsonify({'code': 2, 'msg': f'获取好友列表失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取群成员列表接口
@app.route('/api/groups/<int:group_id>/members', methods=['GET'])
def get_group_members(group_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 检查用户是否是群成员
        cursor.execute("""
            SELECT 1 FROM group_members
            WHERE group_id = %s AND user_id = %s
        """, (group_id, user_id))

        if not cursor.fetchone():
            return jsonify({'code': 3, 'msg': '无权限访问该群'}), 403

        # 获取群成员列表
        cursor.execute("""
            SELECT u.id, u.username, u.avatar, gm.role
            FROM group_members gm
            JOIN user u ON gm.user_id = u.id
            WHERE gm.group_id = %s
            ORDER BY gm.role DESC, u.username
        """, (group_id,))

        members = []
        for row in cursor.fetchall():
            members.append({
                'id': row['id'],
                'name': row['username'],
                'username': row['username'],
                'avatar': row['avatar'],
                'role': row['role']
            })

        return jsonify({'code': 0, 'data': members})

    except Exception as e:
        print(f"获取群成员失败: {str(e)}")
        return jsonify({'code': 2, 'msg': f'获取群成员失败: {str(e)}'}), 500
    finally:
        conn.close()

# 添加群成员接口
@app.route('/api/groups/<int:group_id>/members', methods=['POST'])
def add_group_members(group_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.get_json()
    member_ids = data.get('member_ids', [])

    if not member_ids:
        return jsonify({'code': 2, 'msg': '请选择要添加的成员'}), 400

    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 检查用户是否是群管理员或群主
        cursor.execute("""
            SELECT gm.role, g.owner_id FROM group_members gm
            JOIN `groups` g ON gm.group_id = g.id
            WHERE gm.group_id = %s AND gm.user_id = %s
        """, (group_id, user_id))

        result = cursor.fetchone()
        if not result:
            return jsonify({'code': 3, 'msg': '您不是该群成员'}), 403

        # 如果是群主或管理员，允许添加成员
        is_owner = result['owner_id'] == user_id
        is_admin = result['role'] in ['owner', 'admin']

        if not (is_owner or is_admin):
            return jsonify({'code': 3, 'msg': '无权限添加成员'}), 403

        # 添加成员
        added_count = 0
        added_members = []  # 记录成功添加的成员

        for member_id in member_ids:
            try:
                # 检查是否已经是群成员
                cursor.execute("""
                    SELECT 1 FROM group_members
                    WHERE group_id = %s AND user_id = %s
                """, (group_id, member_id))

                if not cursor.fetchone():
                    # 添加新成员
                    cursor.execute("""
                        INSERT INTO group_members (group_id, user_id, role, joined_at)
                        VALUES (%s, %s, 'member', NOW())
                    """, (group_id, member_id))
                    added_count += 1
                    added_members.append(member_id)
            except Exception as e:
                print(f"添加成员 {member_id} 失败: {str(e)}")
                continue

        conn.commit()

        # 获取群聊信息用于WebSocket通知
        cursor.execute("""
            SELECT g.id, g.name, g.avatar, g.owner_id
            FROM `groups` g
            WHERE g.id = %s
        """, (group_id,))
        group_info = cursor.fetchone()

        # 发送WebSocket通知给新加入的成员


        if added_members and group_info:
            for member_id in added_members:

                if member_id in user_rooms:
                    notification_data = {
                        'type': 'group_member_added',
                        'group_id': group_id,
                        'group_name': group_info['name'],
                        'group_avatar': group_info['avatar'],
                        'owner_id': group_info['owner_id'],
                        'added_by': user_id,
                        'timestamp': datetime.datetime.now().isoformat()
                    }
                    socketio.emit('group_member_change', notification_data, room=user_rooms[member_id])

        return jsonify({'code': 0, 'msg': f'成功添加 {added_count} 名成员'})

    except Exception as e:
        conn.rollback()
        print(f"添加群成员失败: {str(e)}")
        return jsonify({'code': 4, 'msg': f'添加群成员失败: {str(e)}'}), 500
    finally:
        conn.close()

# 设置聊天免打扰接口
@app.route('/api/chats/<int:chat_id>/notification', methods=['PUT'])
def set_chat_notification(chat_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.get_json()
    mute = data.get('mute', False)

    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 检查或创建聊天设置记录
        cursor.execute("""
            SELECT id FROM chat_settings
            WHERE user_id = %s AND chat_id = %s
        """, (user_id, chat_id))

        if cursor.fetchone():
            # 更新现有记录
            cursor.execute("""
                UPDATE chat_settings
                SET muted = %s, updated_at = NOW()
                WHERE user_id = %s AND chat_id = %s
            """, (mute, user_id, chat_id))
        else:
            # 创建新记录
            cursor.execute("""
                INSERT INTO chat_settings (user_id, chat_id, muted, pinned, created_at, updated_at)
                VALUES (%s, %s, %s, 0, NOW(), NOW())
            """, (user_id, chat_id, mute))

        conn.commit()
        return jsonify({'code': 0, 'msg': '设置成功'})

    except Exception as e:
        conn.rollback()
        print(f"设置免打扰失败: {str(e)}")
        return jsonify({'code': 2, 'msg': f'设置失败: {str(e)}'}), 500
    finally:
        conn.close()

# 设置聊天置顶接口
@app.route('/api/chats/<int:chat_id>/pin', methods=['PUT'])
def set_chat_pin(chat_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.get_json()
    pinned = data.get('pinned', False)

    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 检查或创建聊天设置记录
        cursor.execute("""
            SELECT id FROM chat_settings
            WHERE user_id = %s AND chat_id = %s
        """, (user_id, chat_id))

        if cursor.fetchone():
            # 更新现有记录
            cursor.execute("""
                UPDATE chat_settings
                SET pinned = %s, updated_at = NOW()
                WHERE user_id = %s AND chat_id = %s
            """, (pinned, user_id, chat_id))
        else:
            # 创建新记录
            cursor.execute("""
                INSERT INTO chat_settings (user_id, chat_id, muted, pinned, created_at, updated_at)
                VALUES (%s, %s, 0, %s, NOW(), NOW())
            """, (user_id, chat_id, pinned))

        conn.commit()
        return jsonify({'code': 0, 'msg': '设置成功'})

    except Exception as e:
        conn.rollback()
        print(f"设置置顶失败: {str(e)}")
        return jsonify({'code': 2, 'msg': f'设置失败: {str(e)}'}), 500
    finally:
        conn.close()

# 测试API路由
@app.route('/api/test')
def test_api():
    return jsonify({'code': 0, 'msg': '测试成功'})

# 调试API：检查数据库状态
@app.route('/api/debug/db-status')
def debug_db_status():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    cursor = conn.cursor()

    try:
        # 检查好友数量
        cursor.execute("SELECT COUNT(*) as count FROM friends WHERE user_id = %s", (user_id,))
        friends_count = cursor.fetchone()['count']

        # 检查群聊数量
        cursor.execute("SELECT COUNT(*) as count FROM group_members WHERE user_id = %s", (user_id,))
        groups_count = cursor.fetchone()['count']

        # 检查用户信息
        cursor.execute("SELECT id, username, avatar FROM user WHERE id = %s", (user_id,))
        user_info = cursor.fetchone()

        return jsonify({
            'code': 0,
            'data': {
                'user_id': user_id,
                'user_info': user_info,
                'friends_count': friends_count,
                'groups_count': groups_count
            }
        })

    except Exception as e:
        return jsonify({'code': 2, 'msg': f'查询失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取用户背景设置
@app.route('/api/user/cover')
def get_user_cover():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            try:
                cursor.execute('SELECT cover_type, cover_url FROM user WHERE id = %s', (user_id,))
                user = cursor.fetchone()
            except Exception as db_e:
                print(f"数据库查询错误，可能是字段不存在: {db_e}")
                # 如果字段不存在，返回默认值
                cover_data = {
                    'type': 'image',
                    'url': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop'
                }
                return jsonify({'code': 0, 'data': cover_data})

            if not user:
                return jsonify({'code': 2, 'msg': '用户不存在'}), 404

            # 如果没有设置背景，返回默认值
            cover_data = {
                'type': user['cover_type'] or 'image',
                'url': user['cover_url'] or 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop'
            }

            return jsonify({'code': 0, 'data': cover_data})
    except Exception as e:
        print(f"获取用户背景出错: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'code': 500, 'msg': f'服务器错误: {str(e)}'}), 500
    finally:
        conn.close()

# 获取指定用户的背景设置
@app.route('/api/user/cover/<int:target_user_id>')
def get_target_user_cover(target_user_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查是否有权限查看该用户的背景
            # 1. 如果是查看自己的背景，直接允许
            # 2. 如果是查看好友的背景，需要检查是否为好友关系
            if target_user_id != user_id:
                cursor.execute("""
                    SELECT 1 FROM friends
                    WHERE user_id = %s AND friend_id = %s
                """, (user_id, target_user_id))
                if not cursor.fetchone():
                    return jsonify({'code': 2, 'msg': '无权限查看该用户的背景'}), 403

            try:
                cursor.execute('SELECT cover_type, cover_url FROM user WHERE id = %s', (target_user_id,))
                user = cursor.fetchone()
            except Exception as db_e:
                print(f"数据库查询错误，可能是字段不存在: {db_e}")
                # 如果字段不存在，返回默认值
                cover_data = {
                    'type': 'image',
                    'url': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop'
                }
                return jsonify({'code': 0, 'data': cover_data})

            if not user:
                return jsonify({'code': 3, 'msg': '用户不存在'}), 404

            # 如果没有设置背景，返回默认值
            cover_data = {
                'type': user['cover_type'] or 'image',
                'url': user['cover_url'] or 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop'
            }

            return jsonify({'code': 0, 'data': cover_data})
    except Exception as e:
        print(f"获取用户背景出错: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'code': 500, 'msg': f'服务器错误: {str(e)}'}), 500
    finally:
        conn.close()

# 获取指定用户的基本信息
@app.route('/api/user/info/<int:target_user_id>')
def get_target_user_info(target_user_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查是否有权限查看该用户的信息
            # 1. 如果是查看自己的信息，直接允许
            # 2. 如果是查看好友的信息，需要检查是否为好友关系
            if target_user_id != user_id:
                cursor.execute("""
                    SELECT 1 FROM friends
                    WHERE user_id = %s AND friend_id = %s
                """, (user_id, target_user_id))
                if not cursor.fetchone():
                    return jsonify({'code': 2, 'msg': '无权限查看该用户的信息'}), 403

            cursor.execute('SELECT id, username, avatar FROM user WHERE id = %s', (target_user_id,))
            user = cursor.fetchone()

            if not user:
                return jsonify({'code': 3, 'msg': '用户不存在'}), 404

            # 如果没有头像，使用默认头像
            if not user['avatar']:
                user['avatar'] = f"https://picsum.photos/seed/user{user['id']}/100/100"

            return jsonify({'code': 0, 'data': user})
    except Exception as e:
        print(f"获取用户信息出错: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'code': 500, 'msg': f'服务器错误: {str(e)}'}), 500
    finally:
        conn.close()

# 更新用户背景设置
@app.route('/api/user/cover', methods=['POST'])
def update_user_cover():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    try:
        data = request.json
        if not data:
            return jsonify({'code': 2, 'msg': '请求数据为空'}), 400

        cover_type = data.get('type')  # 'image' 或 'video'
        cover_data = data.get('data')  # Base64编码的文件数据



        if not cover_type or cover_type not in ['image', 'video']:
            return jsonify({'code': 3, 'msg': '背景类型无效'}), 400

        if not cover_data:
            return jsonify({'code': 4, 'msg': '背景数据不能为空'}), 400

        conn = get_db_conn()
        try:
            with conn.cursor() as cursor:
                # 检查用户是否存在，同时检查表结构
                try:
                    cursor.execute('SELECT id, cover_type, cover_url FROM user WHERE id = %s', (user_id,))
                    user = cursor.fetchone()
                    if not user:
                        return jsonify({'code': 5, 'msg': '用户不存在'}), 404
                except Exception as db_e:
                    print(f"数据库查询错误，可能是字段不存在: {db_e}")
                    # 尝试添加字段
                    try:
                        cursor.execute('ALTER TABLE user ADD COLUMN cover_type ENUM("image", "video") DEFAULT "image"')
                        cursor.execute('ALTER TABLE user ADD COLUMN cover_url VARCHAR(255)')
                        conn.commit()

                    except Exception as alter_e:
                        print(f"添加字段失败: {alter_e}")
                        return jsonify({'code': 6, 'msg': '数据库结构错误，请联系管理员'}), 500

                # 保存文件
                if cover_type == 'image':
                    file_url = save_base64_image(cover_data, f"cover_{user_id}")
                else:  # video
                    file_url = save_base64_video(cover_data, f"cover_{user_id}")

                if not file_url:
                    return jsonify({'code': 7, 'msg': '文件保存失败'}), 500

                # 更新数据库
                cursor.execute(
                    'UPDATE user SET cover_type = %s, cover_url = %s WHERE id = %s',
                    (cover_type, file_url, user_id)
                )
                conn.commit()

                return jsonify({
                    'code': 0,
                    'msg': '背景更新成功',
                    'data': {
                        'type': cover_type,
                        'url': file_url
                    }
                })
        except Exception as e:
            print(f"数据库操作错误: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'code': 500, 'msg': f'数据库错误: {str(e)}'}), 500
        finally:
            conn.close()
    except Exception as e:
        print(f"更新用户背景出错: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'code': 500, 'msg': f'服务器错误: {str(e)}'}), 500

# 获取好友列表（修改后的联系人接口）
@app.route('/api/contacts')
def get_contacts():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401



    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 获取当前用户信息，包含微信号
            try:
                cursor.execute('SELECT id, username, avatar, wechat_id FROM user WHERE id = %s', (user_id,))
                current_user = cursor.fetchone()
            except Exception as e:
                # 如果wechat_id字段不存在，使用原查询
                if 'wechat_id' in str(e):
                    cursor.execute('SELECT id, username, avatar FROM user WHERE id = %s', (user_id,))
                    current_user = cursor.fetchone()
                    if current_user:
                        current_user['wechat_id'] = None
                else:
                    raise e



            # 查询好友列表，包含微信号
            try:
                query = """
                SELECT u.id, u.username, u.avatar, u.wechat_id
                FROM user u
                JOIN friends f ON u.id = f.friend_id
                WHERE f.user_id = %s
                ORDER BY u.username
                """
                cursor.execute(query, (user_id,))
                friends = cursor.fetchall()
            except Exception as e:
                # 如果wechat_id字段不存在，使用原查询
                if 'wechat_id' in str(e):
                    query = """
                    SELECT u.id, u.username, u.avatar
                    FROM user u
                    JOIN friends f ON u.id = f.friend_id
                    WHERE f.user_id = %s
                    ORDER BY u.username
                    """
                    cursor.execute(query, (user_id,))
                    friends = cursor.fetchall()
                    for friend in friends:
                        friend['wechat_id'] = None
                else:
                    raise e

        conn.close()
    except Exception as e:
        conn.close()
        return jsonify({'code': 2, 'msg': f'获取好友列表失败: {str(e)}'}), 500

    # 格式化结果
    for friend in friends:
        if not friend['avatar']:
            friend['avatar'] = f"https://picsum.photos/seed/user{friend['id']}/100/100"

    # 确保当前用户头像正确
    if current_user and not current_user['avatar']:
        current_user['avatar'] = f"https://picsum.photos/seed/user{current_user['id']}/100/100"

    # 转换日期类型为字符串，解决JSON序列化问题
    if current_user and 'created_at' in current_user:
        current_user['created_at'] = str(current_user['created_at'])

    result = {
        'code': 0,
        'data': {
            'current_user': current_user,
            'contacts': friends,  # 现在只返回好友列表
            'others': []  # 不再返回其他用户，通过搜索功能来发现新用户
        }
    }
    return jsonify(result)

# 获取朋友圈列表
@app.route('/api/moments')
def get_moments():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401
    
    # 分页参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    offset = (page - 1) * limit
    
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 查询朋友圈列表，包括用户信息、媒体数量、点赞数和评论数
            # 只显示当前用户自己的朋友圈和好友的朋友圈
            query = """
            SELECT
                m.id, m.content, m.created_at,
                u.id as user_id, u.username, u.avatar,
                (SELECT COUNT(*) FROM moment_likes WHERE moment_id = m.id) as like_count,
                (SELECT COUNT(*) FROM moment_comments WHERE moment_id = m.id) as comment_count
            FROM moments m
            JOIN user u ON m.user_id = u.id
            WHERE m.user_id = %s OR m.user_id IN (
                SELECT friend_id FROM friends WHERE user_id = %s
            )
            ORDER BY m.created_at DESC
            LIMIT %s, %s
            """
            cursor.execute(query, (user_id, user_id, offset, limit))
            moments_data = cursor.fetchall()
            
            # 格式化结果
            formatted_moments = []
            for moment in moments_data:
                # 查询朋友圈媒体
                cursor.execute("""
                    SELECT media_type, media_url, thumbnail_url 
                    FROM moment_media 
                    WHERE moment_id = %s
                    ORDER BY id
                """, (moment['id'],))
                media = cursor.fetchall()
                
                # 查询点赞用户
                cursor.execute("""
                    SELECT u.id, u.username 
                    FROM moment_likes ml
                    JOIN user u ON ml.user_id = u.id
                    WHERE ml.moment_id = %s
                """, (moment['id'],))
                likes = [like['username'] for like in cursor.fetchall()]
                
                # 查询评论
                cursor.execute("""
                    SELECT 
                        mc.id, mc.content, mc.reply_to,
                        u.username as user,
                        (SELECT username FROM user WHERE id = (
                            SELECT user_id FROM moment_comments WHERE id = mc.reply_to
                        )) as reply_to_user
                    FROM moment_comments mc
                    JOIN user u ON mc.user_id = u.id
                    WHERE mc.moment_id = %s
                    ORDER BY mc.created_at
                """, (moment['id'],))
                comments = cursor.fetchall()
                
                # 格式化时间
                now = datetime.datetime.now()
                created_at = moment['created_at']
                if created_at.date() == now.date():
                    if (now - created_at).seconds < 3600:
                        time_str = f"{(now - created_at).seconds // 60}分钟前"
                    else:
                        time_str = created_at.strftime('%H:%M')
                elif (now.date() - created_at.date()).days == 1:
                    time_str = '昨天'
                else:
                    time_str = created_at.strftime('%m-%d')
                
                # 构造朋友圈数据
                moment_item = {
                    'id': moment['id'],
                    'user': moment['username'],
                    'user_id': moment['user_id'],
                    'avatar': moment['avatar'] or f"https://picsum.photos/seed/user{moment['user_id']}/100/100",
                    'content': moment['content'],
                    'time': time_str,
                    'likes': likes,
                    'comments': comments,
                    'images': [],
                    'video': None
                }
                
                # 处理媒体数据
                for item in media:
                    if item['media_type'] == 'image':
                        moment_item['images'].append(item['media_url'])
                    elif item['media_type'] == 'video':
                        moment_item['video'] = item['media_url']
                
                formatted_moments.append(moment_item)
            
            # 是否有更多数据 - 只统计当前用户可见的朋友圈数量
            cursor.execute("""
                SELECT COUNT(*) as count FROM moments m
                WHERE m.user_id = %s OR m.user_id IN (
                    SELECT friend_id FROM friends WHERE user_id = %s
                )
            """, (user_id, user_id))
            total = cursor.fetchone()['count']
            has_more = total > offset + limit
            
        return jsonify({
            'code': 0, 
            'data': {
                'moments': formatted_moments,
                'has_more': has_more,
                'total': total
            }
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'code': 500, 'msg': str(e)}), 500
    finally:
        conn.close()

# 保存上传的文件
def save_uploaded_file(file, subfolder):
    try:
        # 确保uploads目录存在
        upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], subfolder)
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

        # 生成唯一文件名
        filename = file.filename
        file_ext = os.path.splitext(filename)[1]
        unique_filename = f"{uuid.uuid4().hex}{file_ext}"
        
        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)
        
        # 返回相对路径
        return f"{subfolder}/{unique_filename}"
    except Exception as e:
        print(f"保存上传文件错误: {e}")
        return None

# 保存Base64编码的图片
def save_base64_image(base64_data, file_prefix):
    try:
        # 确保uploads目录存在
        if not os.path.exists(app.config['UPLOAD_FOLDER']):
            os.makedirs(app.config['UPLOAD_FOLDER'])

        # 解析Base64数据
        if ';base64,' in base64_data:
            header, encoded = base64_data.split(';base64,', 1)
            data_type = header.split(':')[1]
            extension = data_type.split('/')[1]
        else:
            # 假设是PNG格式
            encoded = base64_data
            extension = 'png'

        # 生成唯一文件名
        filename = f"{file_prefix}_{uuid.uuid4().hex}.{extension}"

        # 根据文件前缀决定保存目录
        if 'album' in file_prefix:
            subdir = 'albums'
        else:
            subdir = 'images'

        filepath = os.path.join(app.config['UPLOAD_FOLDER'], subdir, filename)

        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # 解码并保存文件
        try:
            decoded_data = base64.b64decode(encoded)
        except Exception as decode_e:
            print(f"Base64解码失败: {decode_e}")
            return None

        with open(filepath, 'wb') as f:
            f.write(decoded_data)

        # 返回完整URL路径，确保路径分隔符正确
        relative_url = os.path.join(app.config['UPLOAD_FOLDER'], subdir, filename).replace('\\', '/')
        full_url = f"/{relative_url}"
        return full_url
    except Exception as e:
        print(f"保存Base64图片出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# 保存Base64编码的视频
def save_base64_video(base64_data, file_prefix):
    try:
        # 确保uploads目录存在
        if not os.path.exists(app.config['UPLOAD_FOLDER']):
            os.makedirs(app.config['UPLOAD_FOLDER'])

        # 解析Base64数据
        if ';base64,' in base64_data:
            header, encoded = base64_data.split(';base64,', 1)
            data_type = header.split(':')[1]
            extension = data_type.split('/')[1]
        else:
            # 假设是MP4格式
            encoded = base64_data
            extension = 'mp4'

        # 生成唯一文件名
        filename = f"{file_prefix}_{uuid.uuid4().hex}.{extension}"

        # 根据文件前缀决定保存目录
        if 'album' in file_prefix:
            subdir = 'albums'
        else:
            subdir = 'videos'

        filepath = os.path.join(app.config['UPLOAD_FOLDER'], subdir, filename)
        print(f"生成文件路径: {filepath}")

        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        print(f"确保目录存在: {os.path.dirname(filepath)}")

        # 解码并保存文件
        try:
            decoded_data = base64.b64decode(encoded)
            print(f"Base64解码成功，数据大小: {len(decoded_data)} bytes")
        except Exception as decode_e:
            print(f"Base64解码失败: {decode_e}")
            return None

        with open(filepath, 'wb') as f:
            f.write(decoded_data)

        print(f"文件保存成功: {filepath}")

        # 返回完整URL路径，确保路径分隔符正确
        relative_url = os.path.join(app.config['UPLOAD_FOLDER'], subdir, filename).replace('\\', '/')
        full_url = f"/{relative_url}"
        print(f"返回完整URL: {full_url}")
        return full_url
    except Exception as e:
        print(f"保存Base64视频出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# 发布朋友圈
@app.route('/api/moments/publish', methods=['POST'])
def publish_moment():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401
    
    data = request.json
    content = data.get('content', '').strip()
    images = data.get('images', [])
    video = data.get('video')
    
    # 验证内容
    if not content and not images and not video:
        return jsonify({'code': 2, 'msg': '内容不能为空'}), 400
    
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 插入朋友圈记录
            now = datetime.datetime.now()
            cursor.execute(
                'INSERT INTO moments (user_id, content, created_at) VALUES (%s, %s, %s)',
                (user_id, content, now)
            )
            moment_id = cursor.lastrowid
            
            # 处理媒体文件
            saved_images = []
            saved_video = None
            
            # 处理图片
            for i, image_data in enumerate(images):
                if image_data:
                    image_url = save_base64_image(image_data, f"moment_{moment_id}_img{i}")
                    if image_url:
                        cursor.execute(
                            'INSERT INTO moment_media (moment_id, media_type, media_url, created_at) VALUES (%s, %s, %s, %s)',
                            (moment_id, 'image', image_url, now)
                        )
                        saved_images.append(image_url)
            
            # 处理视频
            if video:
                # 从Base64保存视频
                if video.startswith('data:video'):
                    pattern = r'data:video/([^;]+);base64,(.+)'
                    match = re.match(pattern, video)
                    if match:
                        video_ext, video_data = match.groups()
                        video_filename = f"moment_{moment_id}_video.{video_ext}"
                        video_path = os.path.join(app.config['UPLOAD_FOLDER'], 'videos', video_filename)
                        
                        with open(video_path, 'wb') as f:
                            f.write(base64.b64decode(video_data))
                            
                        video_url = os.path.join(app.config['UPLOAD_FOLDER'], 'videos', video_filename).replace('\\', '/')
                        cursor.execute(
                            'INSERT INTO moment_media (moment_id, media_type, media_url, created_at) VALUES (%s, %s, %s, %s)',
                            (moment_id, 'video', video_url, now)
                        )
                        saved_video = video_url
            
            conn.commit()
            
            # 查询用户信息
            cursor.execute('SELECT username, avatar FROM user WHERE id = %s', (user_id,))
            user = cursor.fetchone()
            
            # 返回新发布的朋友圈
            return jsonify({
                'code': 0,
                'msg': '发布成功',
                'data': {
                    'id': moment_id,
                    'user': user['username'],
                    'avatar': user['avatar'] or f"https://picsum.photos/seed/user{user_id}/100/100",
                    'content': content,
                    'images': saved_images,
                    'video': saved_video,
                    'time': '刚刚',
                    'likes': [],
                    'comments': []
                }
            })
    except Exception as e:
        conn.rollback()
        import traceback
        traceback.print_exc()
        return jsonify({'code': 3, 'msg': f'发布失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取特定用户的朋友圈列表
@app.route('/api/moments/user/<int:target_user_id>')
def get_user_moments(target_user_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    # 分页参数
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    offset = (page - 1) * limit

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查是否有权限查看该用户的朋友圈
            # 1. 如果是查看自己的朋友圈，直接允许
            # 2. 如果是查看好友的朋友圈，需要检查是否为好友关系
            if target_user_id != user_id:
                cursor.execute("""
                    SELECT 1 FROM friends
                    WHERE user_id = %s AND friend_id = %s
                """, (user_id, target_user_id))
                if not cursor.fetchone():
                    return jsonify({'code': 2, 'msg': '无权限查看该用户的朋友圈'}), 403

            # 查询目标用户的朋友圈列表
            query = """
            SELECT
                m.id, m.content, m.created_at,
                u.id as user_id, u.username, u.avatar,
                (SELECT COUNT(*) FROM moment_likes WHERE moment_id = m.id) as like_count,
                (SELECT COUNT(*) FROM moment_comments WHERE moment_id = m.id) as comment_count
            FROM moments m
            JOIN user u ON m.user_id = u.id
            WHERE m.user_id = %s
            ORDER BY m.created_at DESC
            LIMIT %s, %s
            """
            cursor.execute(query, (target_user_id, offset, limit))
            moments_data = cursor.fetchall()

            # 格式化结果
            formatted_moments = []
            for moment in moments_data:
                # 查询朋友圈媒体
                cursor.execute("""
                    SELECT media_type, media_url, thumbnail_url
                    FROM moment_media
                    WHERE moment_id = %s
                    ORDER BY id
                """, (moment['id'],))
                media = cursor.fetchall()

                # 查询点赞用户
                cursor.execute("""
                    SELECT u.id, u.username
                    FROM moment_likes ml
                    JOIN user u ON ml.user_id = u.id
                    WHERE ml.moment_id = %s
                """, (moment['id'],))
                likes = [like['username'] for like in cursor.fetchall()]

                # 查询评论
                cursor.execute("""
                    SELECT
                        mc.id, mc.content, mc.created_at,
                        u.id as user_id, u.username,
                        reply_u.username as reply_to_username
                    FROM moment_comments mc
                    JOIN user u ON mc.user_id = u.id
                    LEFT JOIN user reply_u ON mc.reply_to = reply_u.id
                    WHERE mc.moment_id = %s
                    ORDER BY mc.created_at ASC
                """, (moment['id'],))
                comments_data = cursor.fetchall()

                comments = []
                for comment in comments_data:
                    comment_item = {
                        'id': comment['id'],
                        'user': comment['username'],
                        'user_id': comment['user_id'],
                        'content': comment['content'],
                        'time': comment['created_at'].strftime('%m-%d %H:%M')
                    }
                    if comment['reply_to_username']:
                        comment_item['reply_to'] = comment['reply_to_username']
                    comments.append(comment_item)

                # 格式化时间
                time_str = moment['created_at'].strftime('%m-%d %H:%M')

                # 构造朋友圈数据
                moment_item = {
                    'id': moment['id'],
                    'user': moment['username'],
                    'user_id': moment['user_id'],
                    'avatar': moment['avatar'] or f"https://picsum.photos/seed/user{moment['user_id']}/100/100",
                    'content': moment['content'],
                    'time': time_str,
                    'likes': likes,
                    'comments': comments,
                    'images': [],
                    'video': None
                }

                # 处理媒体数据
                for item in media:
                    if item['media_type'] == 'image':
                        moment_item['images'].append(item['media_url'])
                    elif item['media_type'] == 'video':
                        moment_item['video'] = item['media_url']

                formatted_moments.append(moment_item)

            # 是否有更多数据
            cursor.execute("""
                SELECT COUNT(*) as count FROM moments m
                WHERE m.user_id = %s
            """, (target_user_id,))
            total = cursor.fetchone()['count']
            has_more = total > offset + limit

        return jsonify({
            'code': 0,
            'data': {
                'moments': formatted_moments,
                'has_more': has_more,
                'total': total
            }
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'code': 500, 'msg': str(e)}), 500
    finally:
        conn.close()

# 检查朋友圈是否对当前用户可见的辅助函数
def is_moment_visible_to_user(cursor, moment_id, user_id):
    """检查指定朋友圈是否对当前用户可见"""
    cursor.execute("""
        SELECT m.user_id FROM moments m
        WHERE m.id = %s AND (
            m.user_id = %s OR m.user_id IN (
                SELECT friend_id FROM friends WHERE user_id = %s
            )
        )
    """, (moment_id, user_id, user_id))
    return cursor.fetchone() is not None

# 点赞/取消点赞
@app.route('/api/moments/<int:moment_id>/like', methods=['POST'])
def toggle_like(moment_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401
    
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查朋友圈是否存在且对当前用户可见
            if not is_moment_visible_to_user(cursor, moment_id, user_id):
                return jsonify({'code': 2, 'msg': '朋友圈不存在或无权限访问'}), 404
            
            # 检查是否已点赞
            cursor.execute('SELECT id FROM moment_likes WHERE moment_id = %s AND user_id = %s', 
                          (moment_id, user_id))
            existing_like = cursor.fetchone()
            
            if existing_like:
                # 取消点赞
                cursor.execute('DELETE FROM moment_likes WHERE id = %s', (existing_like['id'],))
                action = 'unlike'
            else:
                # 点赞
                now = datetime.datetime.now()
                cursor.execute(
                    'INSERT INTO moment_likes (moment_id, user_id, created_at) VALUES (%s, %s, %s)',
                    (moment_id, user_id, now)
                )
                action = 'like'
            
            conn.commit()
            
            # 查询用户名
            cursor.execute('SELECT username FROM user WHERE id = %s', (user_id,))
            username = cursor.fetchone()['username']
            
            return jsonify({
                'code': 0,
                'msg': '操作成功',
                'data': {
                    'moment_id': moment_id,
                    'action': action,
                    'user': username
                }
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 3, 'msg': f'操作失败: {str(e)}'}), 500
    finally:
        conn.close()

# 评论朋友圈
@app.route('/api/moments/<int:moment_id>/comment', methods=['POST'])
def add_comment(moment_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401
    
    data = request.json
    content = data.get('content', '').strip()
    reply_to = data.get('reply_to')
    
    if not content:
        return jsonify({'code': 2, 'msg': '评论内容不能为空'}), 400
    
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查朋友圈是否存在且对当前用户可见
            if not is_moment_visible_to_user(cursor, moment_id, user_id):
                return jsonify({'code': 3, 'msg': '朋友圈不存在或无权限访问'}), 404
            
            # 如果是回复，检查回复的评论是否存在
            if reply_to:
                cursor.execute('SELECT id FROM moment_comments WHERE id = %s AND moment_id = %s', 
                              (reply_to, moment_id))
                if not cursor.fetchone():
                    return jsonify({'code': 4, 'msg': '回复的评论不存在'}), 400
            
            # 插入评论
            now = datetime.datetime.now()
            cursor.execute(
                'INSERT INTO moment_comments (moment_id, user_id, content, reply_to, created_at) VALUES (%s, %s, %s, %s, %s)',
                (moment_id, user_id, content, reply_to, now)
            )
            comment_id = cursor.lastrowid
            conn.commit()
            
            # 查询用户名
            cursor.execute('SELECT username FROM user WHERE id = %s', (user_id,))
            username = cursor.fetchone()['username']
            
            # 如果是回复，查询回复对象的用户名
            reply_to_username = None
            if reply_to:
                cursor.execute("""
                    SELECT u.username 
                    FROM moment_comments mc 
                    JOIN user u ON mc.user_id = u.id 
                    WHERE mc.id = %s
                """, (reply_to,))
                result = cursor.fetchone()
                if result:
                    reply_to_username = result['username']
            
            return jsonify({
                'code': 0,
                'msg': '评论成功',
                'data': {
                    'id': comment_id,
                    'moment_id': moment_id,
                    'user': username,
                    'content': content,
                    'reply_to': reply_to,
                    'reply_to_user': reply_to_username
                }
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 5, 'msg': f'评论失败: {str(e)}'}), 500
    finally:
        conn.close()

# ==================== 相册功能 API ====================

# 获取用户相册列表
@app.route('/api/albums', methods=['GET'])
def get_albums():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 查询用户的所有相册，包含媒体数量
            try:
                # 尝试查询包含cover_position字段
                query = """
                SELECT
                    a.id, a.name, a.description, a.cover_image, a.cover_position, a.created_at,
                    COUNT(am.id) as media_count
                FROM albums a
                LEFT JOIN album_media am ON a.id = am.album_id
                WHERE a.user_id = %s
                GROUP BY a.id, a.name, a.description, a.cover_image, a.cover_position, a.created_at
                ORDER BY a.created_at DESC
                """
                cursor.execute(query, (user_id,))
                albums = cursor.fetchall()
            except Exception as e:
                # 如果字段不存在，使用不包含cover_position的查询
                print(f"cover_position字段可能不存在，使用兼容查询: {e}")
                query = """
                SELECT
                    a.id, a.name, a.description, a.cover_image, a.created_at,
                    COUNT(am.id) as media_count
                FROM albums a
                LEFT JOIN album_media am ON a.id = am.album_id
                WHERE a.user_id = %s
                GROUP BY a.id, a.name, a.description, a.cover_image, a.created_at
                ORDER BY a.created_at DESC
                """
                cursor.execute(query, (user_id,))
                albums = cursor.fetchall()
                # 为每个相册添加默认的cover_position
                for album in albums:
                    album['cover_position'] = 'top'
            return jsonify({
                'code': 0,
                'msg': '获取成功',
                'albums': albums
            })
    except Exception as e:
        return jsonify({'code': 2, 'msg': f'获取相册失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取特定用户的相册列表
@app.route('/api/albums/user/<int:target_user_id>', methods=['GET'])
def get_user_albums(target_user_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查是否有权限查看该用户的相册
            # 1. 如果是查看自己的相册，直接允许
            # 2. 如果是查看好友的相册，需要检查是否为好友关系
            if target_user_id != user_id:
                cursor.execute("""
                    SELECT 1 FROM friends
                    WHERE user_id = %s AND friend_id = %s
                """, (user_id, target_user_id))
                if not cursor.fetchone():
                    return jsonify({'code': 2, 'msg': '无权限查看该用户的相册'}), 403

            # 查询目标用户的所有相册，包含媒体数量
            try:
                # 尝试查询包含cover_position字段
                query = """
                SELECT
                    a.id, a.name, a.description, a.cover_image, a.cover_position, a.created_at,
                    COUNT(am.id) as media_count
                FROM albums a
                LEFT JOIN album_media am ON a.id = am.album_id
                WHERE a.user_id = %s
                GROUP BY a.id, a.name, a.description, a.cover_image, a.cover_position, a.created_at
                ORDER BY a.created_at DESC
                """
                cursor.execute(query, (target_user_id,))
                albums = cursor.fetchall()
            except Exception as e:
                # 如果字段不存在，使用不包含cover_position的查询
                print(f"cover_position字段可能不存在，使用兼容查询: {e}")
                query = """
                SELECT
                    a.id, a.name, a.description, a.cover_image, a.created_at,
                    COUNT(am.id) as media_count
                FROM albums a
                LEFT JOIN album_media am ON a.id = am.album_id
                WHERE a.user_id = %s
                GROUP BY a.id, a.name, a.description, a.cover_image, a.created_at
                ORDER BY a.created_at DESC
                """
                cursor.execute(query, (target_user_id,))
                albums = cursor.fetchall()
                # 为每个相册添加默认的cover_position
                for album in albums:
                    album['cover_position'] = 'top'

            return jsonify({
                'code': 0,
                'msg': '获取成功',
                'albums': albums
            })
    except Exception as e:
        return jsonify({'code': 3, 'msg': f'获取相册失败: {str(e)}'}), 500
    finally:
        conn.close()

# 创建相册
@app.route('/api/albums', methods=['POST'])
def create_album():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    name = data.get('name', '').strip()
    description = data.get('description', '').strip()
    cover_image_data = data.get('cover_image')
    cover_position = data.get('cover_position', 'top')

    if not name:
        return jsonify({'code': 2, 'msg': '相册名称不能为空'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 处理封面图片
            cover_image_url = None
            if cover_image_data and cover_image_data.startswith('data:image'):
                cover_image_url = save_base64_image(cover_image_data, f"album_cover_{user_id}")
                if not cover_image_url:
                    return jsonify({'code': 3, 'msg': '封面图片保存失败'}), 500

            # 创建相册
            now = datetime.datetime.now()
            try:
                # 尝试插入包含cover_position字段
                cursor.execute(
                    'INSERT INTO albums (user_id, name, description, cover_image, cover_position, created_at) VALUES (%s, %s, %s, %s, %s, %s)',
                    (user_id, name, description, cover_image_url, cover_position, now)
                )
            except Exception as e:
                # 如果字段不存在，使用不包含cover_position的插入
                print(f"cover_position字段可能不存在，使用兼容插入: {e}")
                cursor.execute(
                    'INSERT INTO albums (user_id, name, description, cover_image, created_at) VALUES (%s, %s, %s, %s, %s)',
                    (user_id, name, description, cover_image_url, now)
                )
            album_id = cursor.lastrowid
            conn.commit()

            return jsonify({
                'code': 0,
                'msg': '相册创建成功',
                'album': {
                    'id': album_id,
                    'name': name,
                    'description': description,
                    'cover_image': cover_image_url,
                    'cover_position': cover_position,
                    'created_at': now.isoformat(),
                    'media_count': 0
                }
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 4, 'msg': f'创建相册失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取相册详情
@app.route('/api/albums/<int:album_id>', methods=['GET'])
def get_album_detail(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 查询相册信息
            try:
                cursor.execute(
                    'SELECT id, name, description, cover_image, cover_position, created_at FROM albums WHERE id = %s AND user_id = %s',
                    (album_id, user_id)
                )
                album = cursor.fetchone()
            except Exception as e:
                # 如果字段不存在，使用不包含cover_position的查询
                print(f"cover_position字段可能不存在，使用兼容查询: {e}")
                cursor.execute(
                    'SELECT id, name, description, cover_image, created_at FROM albums WHERE id = %s AND user_id = %s',
                    (album_id, user_id)
                )
                album = cursor.fetchone()
                if album:
                    album['cover_position'] = 'top'
            if not album:
                return jsonify({'code': 2, 'msg': '相册不存在'}), 404

            return jsonify({
                'code': 0,
                'msg': '获取成功',
                'album': album
            })
    except Exception as e:
        return jsonify({'code': 3, 'msg': f'获取相册详情失败: {str(e)}'}), 500
    finally:
        conn.close()

# 更新相册信息
@app.route('/api/albums/<int:album_id>', methods=['PUT'])
def update_album(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    name = data.get('name', '').strip()
    description = data.get('description', '').strip()
    cover_image_data = data.get('cover_image')
    cover_position = data.get('cover_position', 'top')

    if not name:
        return jsonify({'code': 2, 'msg': '相册名称不能为空'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查相册是否存在且属于当前用户
            try:
                cursor.execute(
                    'SELECT id, cover_image, cover_position FROM albums WHERE id = %s AND user_id = %s',
                    (album_id, user_id)
                )
            except Exception as e:
                # 如果字段不存在，使用不包含cover_position的查询
                print(f"cover_position字段可能不存在，使用兼容查询: {e}")
                cursor.execute(
                    'SELECT id, cover_image FROM albums WHERE id = %s AND user_id = %s',
                    (album_id, user_id)
                )
            album = cursor.fetchone()

            if not album:
                return jsonify({'code': 3, 'msg': '相册不存在'}), 404

            # 处理封面图片
            cover_image_url = album['cover_image']  # 保持原封面
            if cover_image_data and cover_image_data.startswith('data:image'):
                new_cover_url = save_base64_image(cover_image_data, f"album_cover_{user_id}_{album_id}")
                if new_cover_url:
                    cover_image_url = new_cover_url

            # 更新相册信息
            try:
                cursor.execute(
                    'UPDATE albums SET name = %s, description = %s, cover_image = %s, cover_position = %s WHERE id = %s AND user_id = %s',
                    (name, description, cover_image_url, cover_position, album_id, user_id)
                )
            except Exception as e:
                # 如果字段不存在，使用不包含cover_position的更新
                print(f"cover_position字段可能不存在，使用兼容更新: {e}")
                cursor.execute(
                    'UPDATE albums SET name = %s, description = %s, cover_image = %s WHERE id = %s AND user_id = %s',
                    (name, description, cover_image_url, album_id, user_id)
                )
            conn.commit()

            return jsonify({
                'code': 0,
                'msg': '相册更新成功',
                'album': {
                    'id': album_id,
                    'name': name,
                    'description': description,
                    'cover_image': cover_image_url,
                    'cover_position': cover_position
                }
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 4, 'msg': f'更新相册失败: {str(e)}'}), 500
    finally:
        conn.close()

# 删除相册
@app.route('/api/albums/<int:album_id>', methods=['DELETE'])
def delete_album(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查相册是否存在且属于当前用户
            cursor.execute(
                'SELECT id FROM albums WHERE id = %s AND user_id = %s',
                (album_id, user_id)
            )
            album = cursor.fetchone()

            if not album:
                return jsonify({'code': 2, 'msg': '相册不存在'}), 404

            # 删除相册（由于外键约束，相关的媒体文件也会被删除）
            cursor.execute('DELETE FROM albums WHERE id = %s AND user_id = %s', (album_id, user_id))
            conn.commit()

            return jsonify({
                'code': 0,
                'msg': '相册删除成功'
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 3, 'msg': f'删除相册失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取相册媒体文件
@app.route('/api/albums/<int:album_id>/media', methods=['GET'])
def get_album_media(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查相册是否存在且属于当前用户
            cursor.execute(
                'SELECT id FROM albums WHERE id = %s AND user_id = %s',
                (album_id, user_id)
            )
            album = cursor.fetchone()

            if not album:
                return jsonify({'code': 2, 'msg': '相册不存在'}), 404

            # 查询相册中的媒体文件
            cursor.execute(
                'SELECT id, media_type, media_url, thumbnail_url, file_name, file_size, created_at FROM album_media WHERE album_id = %s ORDER BY created_at DESC',
                (album_id,)
            )
            media = cursor.fetchall()

            return jsonify({
                'code': 0,
                'msg': '获取成功',
                'media': media
            })
    except Exception as e:
        return jsonify({'code': 3, 'msg': f'获取媒体文件失败: {str(e)}'}), 500
    finally:
        conn.close()

# 上传媒体文件到相册（小文件直接上传）
@app.route('/api/albums/<int:album_id>/media', methods=['POST'])
def upload_album_media(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    media_type = data.get('media_type')
    media_data = data.get('media_data')
    file_name = data.get('file_name', '')
    file_size = data.get('file_size', 0)

    if not media_type or not media_data:
        return jsonify({'code': 2, 'msg': '参数不完整'}), 400

    if media_type not in ['image', 'video']:
        return jsonify({'code': 3, 'msg': '不支持的媒体类型'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查相册是否存在且属于当前用户
            cursor.execute(
                'SELECT id FROM albums WHERE id = %s AND user_id = %s',
                (album_id, user_id)
            )
            album = cursor.fetchone()

            if not album:
                return jsonify({'code': 4, 'msg': '相册不存在'}), 404

            # 保存媒体文件
            if media_type == 'image':
                media_url = save_base64_image(media_data, f"album_{album_id}_media")
            else:  # video
                media_url = save_base64_video(media_data, f"album_{album_id}_media")

            if not media_url:
                return jsonify({'code': 5, 'msg': '文件保存失败'}), 500

            # 插入媒体记录
            now = datetime.datetime.now()
            cursor.execute(
                'INSERT INTO album_media (album_id, media_type, media_url, file_name, file_size, created_at) VALUES (%s, %s, %s, %s, %s, %s)',
                (album_id, media_type, media_url, file_name, file_size, now)
            )
            media_id = cursor.lastrowid
            conn.commit()

            return jsonify({
                'code': 0,
                'msg': '上传成功',
                'media': {
                    'id': media_id,
                    'media_type': media_type,
                    'media_url': media_url,
                    'file_name': file_name,
                    'file_size': file_size,
                    'created_at': now.isoformat()
                }
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 6, 'msg': f'上传失败: {str(e)}'}), 500
    finally:
        conn.close()

# 全局变量存储分块上传状态
chunk_uploads = {}

# 初始化分块上传
@app.route('/api/albums/<int:album_id>/media/init-upload', methods=['POST'])
def init_chunk_upload(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    upload_id = data.get('upload_id')
    file_name = data.get('file_name')
    file_size = data.get('file_size')
    media_type = data.get('media_type')
    total_chunks = data.get('total_chunks')

    if not all([upload_id, file_name, file_size, media_type, total_chunks]):
        return jsonify({'code': 2, 'msg': '参数不完整'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查相册是否存在且属于当前用户
            cursor.execute(
                'SELECT id FROM albums WHERE id = %s AND user_id = %s',
                (album_id, user_id)
            )
            album = cursor.fetchone()

            if not album:
                return jsonify({'code': 3, 'msg': '相册不存在'}), 404

            # 初始化上传状态
            chunk_uploads[upload_id] = {
                'user_id': user_id,
                'album_id': album_id,
                'file_name': file_name,
                'file_size': file_size,
                'media_type': media_type,
                'total_chunks': total_chunks,
                'received_chunks': {},
                'temp_dir': os.path.join(app.config['UPLOAD_FOLDER'], 'temp', upload_id)
            }

            # 创建临时目录
            os.makedirs(chunk_uploads[upload_id]['temp_dir'], exist_ok=True)

            return jsonify({
                'code': 0,
                'msg': '初始化成功',
                'upload_id': upload_id
            })
    except Exception as e:
        return jsonify({'code': 4, 'msg': f'初始化失败: {str(e)}'}), 500
    finally:
        conn.close()

# 上传分块
@app.route('/api/albums/<int:album_id>/media/upload-chunk', methods=['POST'])
def upload_chunk(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    upload_id = data.get('upload_id')
    chunk_index = data.get('chunk_index')
    chunk_data = data.get('chunk_data')
    is_last_chunk = data.get('is_last_chunk', False)

    if upload_id not in chunk_uploads:
        return jsonify({'code': 2, 'msg': '上传会话不存在'}), 400

    upload_info = chunk_uploads[upload_id]
    
    if upload_info['user_id'] != user_id or upload_info['album_id'] != album_id:
        return jsonify({'code': 3, 'msg': '权限验证失败'}), 403

    try:
        # 解码并保存分块
        if ';base64,' in chunk_data:
            header, encoded = chunk_data.split(';base64,', 1)
        else:
            encoded = chunk_data

        decoded_data = base64.b64decode(encoded)
        
        # 保存分块到临时文件
        chunk_path = os.path.join(upload_info['temp_dir'], f'chunk_{chunk_index}')
        with open(chunk_path, 'wb') as f:
            f.write(decoded_data)

        upload_info['received_chunks'][chunk_index] = chunk_path

        return jsonify({
            'code': 0,
            'msg': '分块上传成功',
            'chunk_index': chunk_index,
            'received_chunks': len(upload_info['received_chunks']),
            'total_chunks': upload_info['total_chunks']
        })
    except Exception as e:
        return jsonify({'code': 4, 'msg': f'分块上传失败: {str(e)}'}), 500

# 完成分块上传
@app.route('/api/albums/<int:album_id>/media/complete-upload', methods=['POST'])
def complete_chunk_upload(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    upload_id = data.get('upload_id')

    if upload_id not in chunk_uploads:
        return jsonify({'code': 2, 'msg': '上传会话不存在'}), 400

    upload_info = chunk_uploads[upload_id]
    
    if upload_info['user_id'] != user_id or upload_info['album_id'] != album_id:
        return jsonify({'code': 3, 'msg': '权限验证失败'}), 403

    conn = get_db_conn()
    try:
        # 检查所有分块是否都已接收
        if len(upload_info['received_chunks']) != upload_info['total_chunks']:
            return jsonify({'code': 4, 'msg': '分块不完整'}), 400

        # 合并分块
        media_type = upload_info['media_type']
        file_extension = 'mp4' if media_type == 'video' else 'jpg'
        final_filename = f"album_{album_id}_media_{uuid.uuid4().hex}.{file_extension}"
        
        if media_type == 'video':
            final_path = os.path.join(app.config['UPLOAD_FOLDER'], 'albums', final_filename)
        else:
            final_path = os.path.join(app.config['UPLOAD_FOLDER'], 'albums', final_filename)

        # 确保目录存在
        os.makedirs(os.path.dirname(final_path), exist_ok=True)

        # 按顺序合并分块
        with open(final_path, 'wb') as final_file:
            for chunk_index in range(upload_info['total_chunks']):
                chunk_path = upload_info['received_chunks'][chunk_index]
                with open(chunk_path, 'rb') as chunk_file:
                    final_file.write(chunk_file.read())

        # 插入媒体记录
        with conn.cursor() as cursor:
            now = datetime.datetime.now()
            media_url = os.path.join('uploads', 'albums', final_filename).replace('\\', '/')
            
            cursor.execute(
                'INSERT INTO album_media (album_id, media_type, media_url, file_name, file_size, created_at) VALUES (%s, %s, %s, %s, %s, %s)',
                (album_id, media_type, media_url, upload_info['file_name'], upload_info['file_size'], now)
            )
            media_id = cursor.lastrowid
            conn.commit()

            # 清理临时文件
            cleanup_chunk_upload(upload_id)

            return jsonify({
                'code': 0,
                'msg': '上传完成',
                'media': {
                    'id': media_id,
                    'media_type': media_type,
                    'media_url': media_url,
                    'file_name': upload_info['file_name'],
                    'file_size': upload_info['file_size'],
                    'created_at': now.isoformat()
                }
            })
    except Exception as e:
        conn.rollback()
        cleanup_chunk_upload(upload_id)
        return jsonify({'code': 5, 'msg': f'完成上传失败: {str(e)}'}), 500
    finally:
        conn.close()

# 清理分块上传
@app.route('/api/albums/<int:album_id>/media/cleanup-upload', methods=['POST'])
def cleanup_chunk_upload_api(album_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    upload_id = data.get('upload_id')

    cleanup_chunk_upload(upload_id)
    return jsonify({'code': 0, 'msg': '清理完成'})

# 清理分块上传的辅助函数
def cleanup_chunk_upload(upload_id):
    if upload_id in chunk_uploads:
        upload_info = chunk_uploads[upload_id]
        temp_dir = upload_info['temp_dir']
        
        # 删除临时文件和目录
        try:
            import shutil
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"清理临时文件失败: {e}")
        
        # 从内存中删除上传信息
        del chunk_uploads[upload_id]

# 删除相册媒体文件
@app.route('/api/albums/<int:album_id>/media/<int:media_id>', methods=['DELETE'])
def delete_album_media(album_id, media_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查相册是否存在且属于当前用户
            cursor.execute(
                'SELECT id FROM albums WHERE id = %s AND user_id = %s',
                (album_id, user_id)
            )
            album = cursor.fetchone()

            if not album:
                return jsonify({'code': 2, 'msg': '相册不存在'}), 404

            # 检查媒体文件是否存在，并获取文件路径
            cursor.execute(
                'SELECT id, media_url FROM album_media WHERE id = %s AND album_id = %s',
                (media_id, album_id)
            )
            media = cursor.fetchone()

            if not media:
                return jsonify({'code': 3, 'msg': '媒体文件不存在'}), 404

            # 获取文件的实际路径
            media_url = media['media_url']
            if media_url:
                # 构建完整的文件路径
                file_path = media_url
                if not os.path.isabs(file_path):
                    # 如果是相对路径，转换为绝对路径
                    file_path = os.path.join(os.getcwd(), file_path)
                
                # 删除本地文件
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"成功删除本地文件: {file_path}")
                    else:
                        print(f"本地文件不存在: {file_path}")
                except Exception as file_e:
                    print(f"删除本地文件失败: {file_e}")
                    # 即使文件删除失败，也继续删除数据库记录

            # 删除数据库记录
            cursor.execute('DELETE FROM album_media WHERE id = %s AND album_id = %s', (media_id, album_id))
            conn.commit()

            return jsonify({
                'code': 0,
                'msg': '删除成功'
            })
    except Exception as e:
        conn.rollback()
        return jsonify({'code': 4, 'msg': f'删除失败: {str(e)}'}), 500
    finally:
        conn.close()



# 静态文件服务
@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    # 明确排除API路径
    if filename.startswith('api/'):
        abort(404)
    try:
        return send_from_directory('.', filename)
    except:
        return send_from_directory('.', 'index.html')

# ==================== WebSocket 事件处理 ====================

@socketio.on('connect')
def handle_connect():
    """处理客户端连接"""
    user_id = session.get('user_id')
    if not user_id:
        print("未登录用户尝试连接WebSocket")
        disconnect()
        return False

    # 获取用户信息
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            cursor.execute('SELECT username FROM user WHERE id = %s', (user_id,))
            user = cursor.fetchone()
            if not user:
                disconnect()
                return False

            # 记录用户在线状态
            online_users[user_id] = {
                'sid': request.sid,
                'username': user['username'],
                'last_seen': datetime.datetime.now()
            }

            # 更新数据库中的在线状态
            update_user_online_status(user_id, True)

            # 加入用户专属房间（用于接收私人消息）
            user_room = f"user_{user_id}"
            join_room(user_room)
            user_rooms[user_id] = user_room



            # 广播用户上线状态给所有在线用户
            emit('user_online', {
                'user_id': user_id,
                'username': user['username'],
                'timestamp': datetime.datetime.now().isoformat()
            }, broadcast=True)

    except Exception as e:
        print(f"WebSocket连接处理错误: {e}")
        disconnect()
        return False
    finally:
        conn.close()

@socketio.on('disconnect')
def handle_disconnect():
    """处理客户端断开连接"""
    user_id = session.get('user_id')
    if user_id and user_id in online_users:
        username = online_users[user_id]['username']

        # 离开用户房间
        if user_id in user_rooms:
            leave_room(user_rooms[user_id])
            del user_rooms[user_id]

        # 移除在线状态
        del online_users[user_id]

        # 更新数据库中的离线状态
        update_user_online_status(user_id, False)



        # 广播用户下线状态给所有在线用户
        emit('user_offline', {
            'user_id': user_id,
            'username': username,
            'timestamp': datetime.datetime.now().isoformat()
        }, broadcast=True)

@socketio.on('join_chat')
def handle_join_chat(data):
    """用户加入特定聊天房间"""
    user_id = session.get('user_id')
    chat_with_id = data.get('chat_with_id')

    if not user_id or not chat_with_id:
        return

    # 创建聊天房间名称（确保两个用户ID的顺序一致）
    room_name = f"chat_{min(user_id, chat_with_id)}_{max(user_id, chat_with_id)}"
    join_room(room_name)



@socketio.on('leave_chat')
def handle_leave_chat(data):
    """用户离开特定聊天房间"""
    user_id = session.get('user_id')
    chat_with_id = data.get('chat_with_id')

    if not user_id or not chat_with_id:
        return

    # 创建聊天房间名称
    room_name = f"chat_{min(user_id, chat_with_id)}_{max(user_id, chat_with_id)}"
    leave_room(room_name)



@socketio.on('message_read')
def handle_message_read(data):
    """处理消息已读状态更新"""
    user_id = session.get('user_id')
    message_ids = data.get('message_ids', [])
    sender_id = data.get('sender_id')

    if not user_id or not message_ids or not sender_id:
        return

    # 更新数据库中的消息已读状态
    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 批量更新消息已读状态
            placeholders = ','.join(['%s'] * len(message_ids))
            cursor.execute(
                f'UPDATE messages SET read_status = 1 WHERE id IN ({placeholders}) AND receiver_id = %s',
                message_ids + [user_id]
            )
            conn.commit()

            # 通知发送方消息已被读取
            if sender_id in user_rooms:
                socketio.emit('messages_read', {
                    'message_ids': message_ids,
                    'reader_id': user_id,
                    'timestamp': datetime.datetime.now().isoformat()
                }, room=user_rooms[sender_id])

    except Exception as e:
        print(f"更新消息已读状态错误: {e}")
    finally:
        conn.close()

@socketio.on('typing_start')
def handle_typing_start(data):
    """处理用户开始输入"""
    user_id = session.get('user_id')
    chat_with_id = data.get('chat_with_id')

    if not user_id or not chat_with_id:
        return

    # 通知对方用户正在输入
    if chat_with_id in user_rooms:
        socketio.emit('user_typing', {
            'user_id': user_id,
            'typing': True,
            'timestamp': datetime.datetime.now().isoformat()
        }, room=user_rooms[chat_with_id])

@socketio.on('typing_stop')
def handle_typing_stop(data):
    """处理用户停止输入"""
    user_id = session.get('user_id')
    chat_with_id = data.get('chat_with_id')

    if not user_id or not chat_with_id:
        return

    # 通知对方用户停止输入
    if chat_with_id in user_rooms:
        socketio.emit('user_typing', {
            'user_id': user_id,
            'typing': False,
            'timestamp': datetime.datetime.now().isoformat()
        }, room=user_rooms[chat_with_id])

@socketio.on('heartbeat')
def handle_heartbeat():
    """处理心跳检测"""
    user_id = session.get('user_id')
    if not user_id:
        return

    # 更新用户最后活跃时间
    if user_id in online_users:
        online_users[user_id]['last_seen'] = datetime.datetime.now()

    # 响应心跳
    emit('heartbeat_response', {
        'timestamp': datetime.datetime.now().isoformat()
    })

@socketio.on('error')
def handle_error(error):
    """处理WebSocket错误"""
    user_id = session.get('user_id')
    print(f"WebSocket错误 (用户ID: {user_id}): {error}")

    # 可以在这里添加错误日志记录或其他错误处理逻辑

# 辅助函数：通过WebSocket发送新消息
def send_message_via_websocket(sender_id, receiver_id, message_data):
    """通过WebSocket发送新消息给接收方"""
    try:
        # 只发送给接收方，不发送给发送方
        if receiver_id in user_rooms:
            socketio.emit('new_message', message_data, room=user_rooms[receiver_id])

    except Exception as e:
        print(f"WebSocket发送消息错误: {e}")

# 获取在线用户列表的API
@app.route('/api/online-users')
def get_online_users_api():
    """获取当前在线用户列表"""
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    online_list = []
    for uid, info in online_users.items():
        online_list.append({
            'user_id': uid,
            'username': info['username'],
            'last_seen': info['last_seen'].isoformat()
        })

    return jsonify({'code': 0, 'data': online_list})

# 获取特定用户的在线状态
@app.route('/api/users/status', methods=['POST'])
def get_users_status():
    """获取指定用户列表的在线状态"""
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    user_ids = data.get('user_ids', [])

    if not user_ids:
        return jsonify({'code': 2, 'msg': '用户ID列表不能为空'}), 400

    # 获取用户在线状态
    status_dict = get_user_online_status(user_ids)

    return jsonify({'code': 0, 'data': status_dict})

# ==================== 群聊功能API ====================

# 创建群聊
@app.route('/api/groups', methods=['POST'])
def create_group():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    # 获取表单数据
    group_name = request.form.get('name')
    member_ids_str = request.form.get('member_ids')
    
    if not group_name or not member_ids_str:
        return jsonify({'code': 2, 'msg': '群聊名称和成员列表不能为空'}), 400

    try:
        import json
        member_ids = json.loads(member_ids_str)
    except:
        return jsonify({'code': 3, 'msg': '成员列表格式错误'}), 400

    if len(member_ids) < 2:
        return jsonify({'code': 4, 'msg': '群聊至少需要2个成员'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查群聊表是否存在，如果不存在则创建
            try:
                cursor.execute("DESCRIBE `groups`")
                # 检查是否有owner_id列
                columns = cursor.fetchall()
                has_owner_id = any(col['Field'] == 'owner_id' for col in columns)
                if not has_owner_id:
                    # 如果表存在但缺少owner_id列，先删除表重新创建
                    cursor.execute("DROP TABLE IF EXISTS group_messages")
                    cursor.execute("DROP TABLE IF EXISTS group_members")
                    cursor.execute("DROP TABLE IF EXISTS `groups`")
                    raise Exception("Table structure incomplete, recreating...")
            except:
                # 表不存在或结构不完整，创建表
                # 按依赖顺序删除表
                cursor.execute("DROP TABLE IF EXISTS group_messages")
                cursor.execute("DROP TABLE IF EXISTS group_members")
                cursor.execute("DROP TABLE IF EXISTS `groups`")
                
                # 创建群聊表
                cursor.execute("""
                    CREATE TABLE `groups` (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(50) NOT NULL,
                        avatar VARCHAR(255),
                        owner_id INT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (owner_id) REFERENCES user(id)
                    )
                """)
                
                # 创建群成员表
                cursor.execute("""
                    CREATE TABLE group_members (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        group_id INT NOT NULL,
                        user_id INT NOT NULL,
                        role ENUM('owner', 'admin', 'member') DEFAULT 'member',
                        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (group_id) REFERENCES `groups`(id) ON DELETE CASCADE,
                        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_group_member (group_id, user_id)
                    )
                """)
                
                # 创建群消息表（如果需要的话）
                cursor.execute("""
                    CREATE TABLE group_messages (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        group_id INT NOT NULL,
                        sender_id INT NOT NULL,
                        content TEXT,
                        message_type VARCHAR(20) DEFAULT 'text',
                        file_url VARCHAR(255),
                        file_name VARCHAR(255),
                        file_size INT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (group_id) REFERENCES `groups`(id) ON DELETE CASCADE,
                        FOREIGN KEY (sender_id) REFERENCES user(id) ON DELETE CASCADE
                    )
                """)
                
                # 创建群聊消息已读状态表
                cursor.execute("""
                    CREATE TABLE group_message_reads (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        message_id INT NOT NULL,
                        user_id INT NOT NULL,
                        read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (message_id) REFERENCES group_messages(id) ON DELETE CASCADE,
                        FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_message_read (message_id, user_id)
                    )
                """)
                
                conn.commit()
                print("群聊相关表创建成功")

            # 禁止群头像上传，群头像将由前端使用成员头像九宫格显示
            avatar_url = None

            # 创建群聊
            cursor.execute(
                'INSERT INTO `groups` (name, avatar, owner_id) VALUES (%s, %s, %s)',
                (group_name, avatar_url, user_id)
            )
            group_id = cursor.lastrowid

            # 添加群主到群成员
            cursor.execute(
                'INSERT INTO group_members (group_id, user_id) VALUES (%s, %s)',
                (group_id, user_id)
            )

            # 添加其他成员到群聊
            for member_id in member_ids:
                try:
                    cursor.execute(
                        'INSERT INTO group_members (group_id, user_id) VALUES (%s, %s)',
                        (group_id, member_id)
                    )
                except Exception as e:
                    print(f"添加成员 {member_id} 失败: {e}")

            conn.commit()

            return jsonify({
                'code': 0,
                'msg': '群聊创建成功',
                'data': {
                    'id': group_id,
                    'name': group_name,
                    'avatar': avatar_url
                }
            })

    except Exception as e:
        print(f"创建群聊错误: {e}")
        return jsonify({'code': 5, 'msg': f'创建群聊失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取群聊列表
@app.route('/api/groups')
def get_groups():
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查群聊表是否存在
            try:
                cursor.execute("SELECT 1 FROM `groups` LIMIT 1")
            except:
                # 表不存在，返回空列表
                return jsonify({'code': 0, 'data': []})

            # 获取用户参与的群聊
            cursor.execute("""
                SELECT g.id, g.name, g.avatar, g.owner_id, g.created_at,
                       COUNT(gm.user_id) as member_count
                FROM `groups` g
                JOIN group_members gm1 ON g.id = gm1.group_id AND gm1.user_id = %s
                LEFT JOIN group_members gm ON g.id = gm.group_id
                GROUP BY g.id, g.name, g.avatar, g.owner_id, g.created_at
                ORDER BY g.created_at DESC
            """, (user_id,))

            groups = cursor.fetchall()

            # 为每个群获取前9个成员头像，用于生成群头像马赛克
            for group in groups:
                group['created_at'] = group['created_at'].isoformat() if group['created_at'] else None
                try:
                    cursor.execute(
                        """
                        SELECT u.avatar, u.username, gm.role
                        FROM group_members gm
                        JOIN user u ON gm.user_id = u.id
                        WHERE gm.group_id = %s
                        ORDER BY
                            CASE WHEN gm.role = 'owner' THEN 0 ELSE 1 END,
                            CASE WHEN u.id = %s THEN 0 ELSE 1 END,
                            u.username
                        LIMIT 9
                        """,
                        (group['id'], user_id)
                    )
                    avatars = []
                    for row in cursor.fetchall():
                        if row['avatar']:
                            avatars.append(row['avatar'])
                        else:
                            # 生成首字母占位，前端可用此占位颜色/字母渲染
                            name = row.get('username') or ''
                            initial = name[:1].upper() if name else ''
                            avatars.append(f"letter:{initial}")
                except Exception:
                    avatars = []
                group['member_avatars'] = avatars

            return jsonify({'code': 0, 'data': groups})

    except Exception as e:
        print(f"获取群聊列表错误: {e}")
        return jsonify({'code': 2, 'msg': f'获取群聊列表失败: {str(e)}'}), 500
    finally:
        conn.close()

# 移除群员接口
@app.route('/api/groups/<int:group_id>/members/<int:member_id>', methods=['DELETE'])
def remove_group_member(group_id, member_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查当前用户是否是群主
            cursor.execute("SELECT role FROM group_members WHERE group_id = %s AND user_id = %s", (group_id, user_id))
            current_user_role = cursor.fetchone()

            if not current_user_role or current_user_role['role'] != 'owner':
                return jsonify({'code': 2, 'msg': '只有群主才能移除群员'}), 403

            # 检查要移除的用户是否在群中
            cursor.execute("SELECT role, user_id FROM group_members WHERE group_id = %s AND user_id = %s", (group_id, member_id))
            target_member = cursor.fetchone()

            if not target_member:
                return jsonify({'code': 3, 'msg': '该用户不在群聊中'}), 400

            # 不能移除群主自己
            if target_member['role'] == 'owner':
                return jsonify({'code': 4, 'msg': '不能移除群主'}), 400

            # 获取被移除用户的信息
            cursor.execute("SELECT username FROM user WHERE id = %s", (member_id,))
            user_info = cursor.fetchone()
            user_name = user_info['username'] if user_info else f'用户{member_id}'

            # 从群中移除用户
            cursor.execute("DELETE FROM group_members WHERE group_id = %s AND user_id = %s", (group_id, member_id))
            conn.commit()

            # 发送WebSocket通知给被移除的用户
            print(f"准备发送移除通知给用户 {member_id}")
            print(f"当前user_rooms: {user_rooms}")
            print(f"用户 {member_id} 是否在user_rooms中: {member_id in user_rooms}")

            if member_id in user_rooms:
                notification_data = {
                    'type': 'group_member_removed',
                    'group_id': group_id,
                    'removed_by': user_id,
                    'timestamp': datetime.datetime.now().isoformat()
                }
                print(f"发送WebSocket移除通知数据: {notification_data}")
                print(f"发送到房间: {user_rooms[member_id]}")
                socketio.emit('group_member_change', notification_data, room=user_rooms[member_id])
                print(f"✅ 已发送群成员移除通知给用户 {member_id}")
            else:
                print(f"❌ 用户 {member_id} 不在user_rooms中，无法发送移除通知")

            return jsonify({'code': 0, 'msg': f'已将 "{user_name}" 移出群聊'})

    except Exception as e:
        print(f"移除群员失败: {e}")
        return jsonify({'code': 5, 'msg': f'移除群员失败: {str(e)}'}), 500
    finally:
        conn.close()

# 退出群聊
@app.route('/api/groups/<int:group_id>/leave', methods=['POST'])
def leave_group(group_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查群聊是否存在
            cursor.execute('SELECT owner_id FROM `groups` WHERE id = %s', (group_id,))
            group = cursor.fetchone()
            
            if not group:
                return jsonify({'code': 2, 'msg': '群聊不存在'}), 404

            # 群主不能直接退出，需要先解散群聊
            if group['owner_id'] == user_id:
                return jsonify({'code': 3, 'msg': '群主不能退出群聊，请先解散群聊'}), 400

            # 检查用户是否是群成员
            cursor.execute(
                'SELECT 1 FROM group_members WHERE group_id = %s AND user_id = %s',
                (group_id, user_id)
            )
            if not cursor.fetchone():
                return jsonify({'code': 4, 'msg': '您不是该群聊成员'}), 400

            # 从群聊中移除用户
            cursor.execute(
                'DELETE FROM group_members WHERE group_id = %s AND user_id = %s',
                (group_id, user_id)
            )
            conn.commit()

            # 发送WebSocket通知给退出的用户（自己）
            if user_id in user_rooms:
                notification_data = {
                    'type': 'group_member_left',
                    'group_id': group_id,
                    'timestamp': datetime.datetime.now().isoformat()
                }
                socketio.emit('group_member_change', notification_data, room=user_rooms[user_id])
                print(f"发送群聊退出通知给用户 {user_id}")

            return jsonify({'code': 0, 'msg': '退出群聊成功'})

    except Exception as e:
        print(f"退出群聊错误: {e}")
        return jsonify({'code': 5, 'msg': f'退出群聊失败: {str(e)}'}), 500
    finally:
        conn.close()

# 解散群聊
@app.route('/api/groups/<int:group_id>/dissolve', methods=['POST'])
def dissolve_group(group_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查群聊是否存在且用户是群主
            cursor.execute('SELECT owner_id FROM `groups` WHERE id = %s', (group_id,))
            group = cursor.fetchone()
            
            if not group:
                return jsonify({'code': 2, 'msg': '群聊不存在'}), 404

            if group['owner_id'] != user_id:
                return jsonify({'code': 3, 'msg': '只有群主可以解散群聊'}), 403

            # 删除群聊（级联删除会自动删除群成员）
            cursor.execute('DELETE FROM `groups` WHERE id = %s', (group_id,))
            conn.commit()

            return jsonify({'code': 0, 'msg': '群聊解散成功'})

    except Exception as e:
        print(f"解散群聊错误: {e}")
        return jsonify({'code': 4, 'msg': f'解散群聊失败: {str(e)}'}), 500
    finally:
        conn.close()

# ==================== 群聊功能API结束 ====================

# 获取群聊详情
@app.route('/api/groups/<int:group_id>/detail')
def get_group_detail_with_settings(group_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查用户是否是群成员
            cursor.execute("""
                SELECT 1 FROM group_members
                WHERE group_id = %s AND user_id = %s
            """, (group_id, user_id))

            if not cursor.fetchone():
                return jsonify({'code': 3, 'msg': '无权限访问该群'}), 403

            # 获取群聊基本信息
            cursor.execute("""
                SELECT g.id, g.name, g.avatar, g.owner_id, g.created_at,
                       COUNT(gm.user_id) as member_count,
                       COALESCE(cs.muted, 0) as muted,
                       COALESCE(cs.pinned, 0) as pinned
                FROM `groups` g
                LEFT JOIN group_members gm ON g.id = gm.group_id
                LEFT JOIN chat_settings cs ON cs.user_id = %s AND cs.chat_id = g.id
                WHERE g.id = %s
                GROUP BY g.id, g.name, g.avatar, g.owner_id, g.created_at, cs.muted, cs.pinned
            """, (user_id, group_id))

            group = cursor.fetchone()
            if not group:
                return jsonify({'code': 2, 'msg': '群聊不存在'}), 404

            # 获取群成员列表，群主排在第一位
            cursor.execute("""
                SELECT u.id, u.username, u.avatar, gm.role
                FROM group_members gm
                JOIN user u ON gm.user_id = u.id
                WHERE gm.group_id = %s
                ORDER BY
                    CASE WHEN gm.role = 'owner' THEN 0 ELSE 1 END,
                    gm.role DESC,
                    u.username
            """, (group_id,))

            members = []
            for row in cursor.fetchall():
                members.append({
                    'id': row['id'],
                    'name': row['username'],
                    'username': row['username'],
                    'avatar': row['avatar'],
                    'role': row['role']
                })

            return jsonify({
                'code': 0,
                'data': {
                    'id': group['id'],
                    'name': group['name'],
                    'avatar': group['avatar'],  # 返回真实头像或空值，让前端处理默认显示
                    'owner_id': group['owner_id'],
                    'member_count': group['member_count'],
                    'members': members,
                    'muted': bool(group['muted']),
                    'pinned': bool(group['pinned'])
                }
            })

    except Exception as e:
        print(f"获取群聊详情失败: {str(e)}")
        return jsonify({'code': 4, 'msg': f'获取群聊详情失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取群聊消息
@app.route('/api/group_messages/<int:group_id>')
def get_group_messages(group_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查用户是否是群成员
            cursor.execute(
                'SELECT 1 FROM group_members WHERE group_id = %s AND user_id = %s',
                (group_id, user_id)
            )
            if not cursor.fetchone():
                return jsonify({'code': 2, 'msg': '无权限访问该群聊'}), 403

            # 获取群聊信息
            cursor.execute(
                'SELECT id, name, avatar, owner_id FROM `groups` WHERE id = %s',
                (group_id,)
            )
            group = cursor.fetchone()
            
            if not group:
                return jsonify({'code': 3, 'msg': '群聊不存在'}), 404

            # 获取群成员数量
            cursor.execute(
                'SELECT COUNT(*) as member_count FROM group_members WHERE group_id = %s',
                (group_id,)
            )
            member_count_result = cursor.fetchone()
            member_count = member_count_result['member_count'] if member_count_result else 0

            # 获取群聊消息
            cursor.execute("""
                SELECT gm.id, gm.content, gm.message_type, gm.file_url, gm.file_name,
                       gm.file_size, gm.created_at, gm.sender_id, u.username, u.avatar
                FROM group_messages gm
                JOIN user u ON gm.sender_id = u.id
                WHERE gm.group_id = %s
                ORDER BY gm.created_at DESC
                LIMIT 50
            """, (group_id,))

            messages = list(cursor.fetchall())
            messages.reverse()  # 按时间正序排列

            # 获取所有消息的已读状态
            message_ids = [msg['id'] for msg in messages]
            read_status_map = {}

            if message_ids:
                placeholders = ','.join(['%s'] * len(message_ids))
                cursor.execute(f"""
                    SELECT gmr.message_id, COUNT(*) as read_count
                    FROM group_message_reads gmr
                    JOIN group_messages gm ON gmr.message_id = gm.id
                    WHERE gmr.message_id IN ({placeholders}) AND gmr.user_id != gm.sender_id
                    GROUP BY gmr.message_id
                """, message_ids)

                for row in cursor.fetchall():
                    read_status_map[row['message_id']] = row['read_count']

            # 格式化消息
            formatted_messages = []
            for msg in messages:
                # 判断消息类型
                msg_type = 'sent' if msg['sender_id'] == user_id else 'received'

                # 格式化时间
                created_at = msg['created_at']
                now = datetime.datetime.now()
                if created_at.date() == now.date():
                    time_str = created_at.strftime('%H:%M')
                elif (now.date() - created_at.date()).days == 1:
                    time_str = '昨天 ' + created_at.strftime('%H:%M')
                else:
                    time_str = created_at.strftime('%m-%d %H:%M')

                # 获取已读人数（排除发送者自己）
                read_count = read_status_map.get(msg['id'], 0)

                formatted_messages.append({
                    'id': msg['id'],
                    'type': msg_type,
                    'content': msg['content'],
                    'message_type': msg['message_type'] or 'text',
                    'file_url': msg['file_url'],
                    'file_name': msg['file_name'],
                    'file_size': msg['file_size'],
                    'time': time_str,
                    'sender_name': msg['username'],
                    'sender_avatar': msg['avatar'],
                    'read_count': read_count,
                    'total_members': member_count - 1 if msg['sender_id'] != user_id else member_count - 1  # 排除发送者
                })

        conn.close()
        
        return jsonify({
            'code': 0,
            'data': {
                'chat': {
                    'id': group['id'],
                    'name': f"{group['name']}({member_count})",
                    'avatar': group['avatar'],  # 返回真实头像或空值，让前端处理默认显示
                    'type': 'group',
                    'owner_id': group['owner_id'],
                    'member_count': member_count
                },
                'messages': formatted_messages
            }
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'code': 500, 'msg': str(e)}), 500

# 发送群聊消息
@app.route('/api/group_messages/<int:group_id>', methods=['POST'])
def send_group_message(group_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 检查用户是否是群成员
            cursor.execute(
                'SELECT 1 FROM group_members WHERE group_id = %s AND user_id = %s',
                (group_id, user_id)
            )
            if not cursor.fetchone():
                return jsonify({'code': 2, 'msg': '无权限在该群聊发送消息'}), 403

            # 获取消息内容
            content = request.form.get('content', '').strip()
            message_type = request.form.get('message_type', 'text')
            
            file_url = request.form.get('file_url')
            file_name = request.form.get('file_name')
            file_size = request.form.get('file_size')
            
            # 如果没有提供文件信息，检查是否有文件上传
            if not file_url and 'file' in request.files:
                uploaded_file = request.files['file']
                if uploaded_file and uploaded_file.filename:
                    file_url = save_uploaded_file(uploaded_file, 'group_messages')
                    file_name = uploaded_file.filename
                    file_size = len(uploaded_file.read())
                    uploaded_file.seek(0)  # 重置文件指针

            # 转换file_size为整数
            if file_size:
                try:
                    file_size = int(file_size)
                except (ValueError, TypeError):
                    file_size = None

            # 验证消息内容
            if not content and not file_url:
                return jsonify({'code': 3, 'msg': '消息内容不能为空'}), 400

            # 插入群聊消息
            cursor.execute(
                '''INSERT INTO group_messages (group_id, sender_id, content, message_type, file_url, file_name, file_size)
                   VALUES (%s, %s, %s, %s, %s, %s, %s)''',
                (group_id, user_id, content, message_type, file_url, file_name, file_size)
            )
            message_id = cursor.lastrowid
            conn.commit()

            # 获取发送者信息
            cursor.execute('SELECT username, avatar FROM user WHERE id = %s', (user_id,))
            sender = cursor.fetchone()

            # 获取群成员列表（用于WebSocket广播）
            cursor.execute(
                'SELECT user_id FROM group_members WHERE group_id = %s',
                (group_id,)
            )
            members = [row['user_id'] for row in cursor.fetchall()]

            # 构造消息数据
            message_data = {
                'id': message_id,
                'group_id': group_id,
                'sender_id': user_id,
                'sender_name': sender['username'],
                'sender_avatar': sender['avatar'],
                'content': content,
                'message_type': message_type,
                'file_url': file_url,
                'file_name': file_name,
                'file_size': file_size,
                'time': datetime.datetime.now().strftime('%H:%M'),
                'created_at': datetime.datetime.now().isoformat()
            }

            # 通过WebSocket广播给所有群成员（除了发送者自己）
            for member_id in members:
                if member_id != user_id and member_id in user_rooms:
                    room = user_rooms[member_id]
                    socketio.emit('group_message', message_data, room=room)

            return jsonify({
                'code': 0,
                'msg': '消息发送成功',
                'data': message_data
            })

    except Exception as e:
        print(f"发送群聊消息错误: {e}")
        return jsonify({'code': 500, 'msg': f'发送消息失败: {str(e)}'}), 500
    finally:
        conn.close()

# 标记群聊消息已读
@app.route('/api/group_messages/<int:group_id>/mark_read', methods=['POST'])
def mark_group_messages_read(group_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    data = request.json
    message_ids = data.get('message_ids', [])

    if not message_ids:
        return jsonify({'code': 2, 'msg': '消息ID列表不能为空'}), 400

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 验证用户是否为群成员
            cursor.execute(
                'SELECT 1 FROM group_members WHERE group_id = %s AND user_id = %s',
                (group_id, user_id)
            )
            if not cursor.fetchone():
                return jsonify({'code': 3, 'msg': '您不是该群的成员'}), 403

            # 批量插入已读记录（使用 INSERT IGNORE 避免重复）
            read_records = []
            for message_id in message_ids:
                read_records.append((message_id, user_id))

            if read_records:
                cursor.executemany(
                    'INSERT IGNORE INTO group_message_reads (message_id, user_id) VALUES (%s, %s)',
                    read_records
                )
                conn.commit()

                # 获取这些消息的发送者，用于WebSocket通知
                placeholders = ','.join(['%s'] * len(message_ids))
                cursor.execute(
                    f'SELECT DISTINCT sender_id FROM group_messages WHERE id IN ({placeholders})',
                    message_ids
                )
                senders = [row['sender_id'] for row in cursor.fetchall()]

                # 准备WebSocket通知数据
                notification_data = {
                    'group_id': group_id,
                    'message_ids': message_ids,
                    'reader_id': user_id,
                    'timestamp': datetime.datetime.now().isoformat()
                }

                # 只通知消息的发送者，而不是所有群成员
                for sender_id in senders:
                    if sender_id in user_rooms and sender_id != user_id:
                        socketio.emit('group_messages_read', notification_data, room=user_rooms[sender_id])

            return jsonify({
                'code': 0,
                'msg': '标记已读成功',
                'data': {
                    'marked_count': len(read_records)
                }
            })

    except Exception as e:
        print(f"标记群聊消息已读错误: {e}")
        return jsonify({'code': 500, 'msg': f'标记已读失败: {str(e)}'}), 500
    finally:
        conn.close()

# 获取群聊消息已读状态
@app.route('/api/group_messages/<int:message_id>/read_status', methods=['GET'])
def get_group_message_read_status(message_id):
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'code': 1, 'msg': '未登录'}), 401

    conn = get_db_conn()
    try:
        with conn.cursor() as cursor:
            # 获取消息信息和群ID
            cursor.execute(
                'SELECT group_id, sender_id FROM group_messages WHERE id = %s',
                (message_id,)
            )
            message_info = cursor.fetchone()
            if not message_info:
                return jsonify({'code': 2, 'msg': '消息不存在'}), 404

            group_id = message_info['group_id']

            # 验证用户是否为群成员
            cursor.execute(
                'SELECT 1 FROM group_members WHERE group_id = %s AND user_id = %s',
                (group_id, user_id)
            )
            if not cursor.fetchone():
                return jsonify({'code': 3, 'msg': '您不是该群的成员'}), 403

            # 获取已读用户列表（排除发送者自己）
            cursor.execute(
                '''SELECT u.id, u.username, u.avatar, gmr.read_at
                   FROM group_message_reads gmr
                   JOIN user u ON gmr.user_id = u.id
                   WHERE gmr.message_id = %s AND gmr.user_id != %s
                   ORDER BY gmr.read_at ASC''',
                (message_id, message_info['sender_id'])
            )
            read_users = cursor.fetchall()

            # 获取群成员总数（排除发送者，因为发送者默认已读）
            cursor.execute(
                'SELECT COUNT(*) as total FROM group_members WHERE group_id = %s AND user_id != %s',
                (group_id, message_info['sender_id'])
            )
            total_members = cursor.fetchone()['total']

            return jsonify({
                'code': 0,
                'data': {
                    'message_id': message_id,
                    'read_count': len(read_users),
                    'total_members': total_members,
                    'read_users': [
                        {
                            'id': user['id'],
                            'username': user['username'],
                            'avatar': user['avatar'],
                            'read_at': user['read_at'].isoformat() if user['read_at'] else None
                        }
                        for user in read_users
                    ]
                }
            })

    except Exception as e:
        print(f"获取群聊消息已读状态错误: {e}")
        return jsonify({'code': 500, 'msg': f'获取已读状态失败: {str(e)}'}), 500
    finally:
        conn.close()

if __name__ == '__main__':
    # 使用SocketIO运行应用
    socketio.run(app, host='0.0.0.0', port=8080, debug=True)